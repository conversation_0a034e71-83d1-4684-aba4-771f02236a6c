import { createTheme } from '@mui/material/styles';

// Define our minimalistic color palette inspired by Material Design 3
const colors = {
  // Primary colors - Modern blue for productivity
  primary: {
    main: '#1976d2',      // Material Blue 700
    light: '#42a5f5',     // Material Blue 400
    dark: '#1565c0',      // Material Blue 800
    contrastText: '#ffffff',
  },
  // Secondary colors - Subtle accent
  secondary: {
    main: '#757575',      // Material Grey 600
    light: '#9e9e9e',     // Material Grey 500
    dark: '#424242',      // Material Grey 800
    contrastText: '#ffffff',
  },
  // Error colors
  error: {
    main: '#d32f2f',      // Material Red 700
    light: '#ef5350',     // Material Red 400
    dark: '#c62828',      // Material Red 800
    contrastText: '#ffffff',
  },
  // Warning colors
  warning: {
    main: '#ed6c02',      // Material Orange 700
    light: '#ff9800',     // Material Orange 500
    dark: '#e65100',      // Material Orange 900
    contrastText: '#ffffff',
  },
  // Info colors
  info: {
    main: '#0288d1',      // Material Light Blue 700
    light: '#03a9f4',     // Material Light Blue 500
    dark: '#01579b',      // Material Light Blue 900
    contrastText: '#ffffff',
  },
  // Success colors
  success: {
    main: '#2e7d32',      // Material Green 700
    light: '#4caf50',     // Material Green 500
    dark: '#1b5e20',      // Material Green 900
    contrastText: '#ffffff',
  },
  // Neutral/Surface colors for minimalistic design
  grey: {
    50: '#fafafa',        // Very light grey for backgrounds
    100: '#f5f5f5',       // Light grey for surfaces
    200: '#eeeeee',       // Border grey
    300: '#e0e0e0',       // Divider grey
    400: '#bdbdbd',       // Disabled text
    500: '#9e9e9e',       // Secondary text
    600: '#757575',       // Primary text light
    700: '#616161',       // Primary text
    800: '#424242',       // Primary text dark
    900: '#212121',       // Darkest text
  },
};

// Create the minimalistic Material Design theme
export const theme = createTheme({
  palette: {
    mode: 'light',
    primary: colors.primary,
    secondary: colors.secondary,
    error: colors.error,
    warning: colors.warning,
    info: colors.info,
    success: colors.success,
    grey: colors.grey,
    background: {
      default: '#fafafa',   // Very light grey for page background
      paper: '#ffffff',     // White for cards and surfaces
    },
    text: {
      primary: colors.grey[800],     // Dark grey for primary text
      secondary: colors.grey[600],   // Medium grey for secondary text
      disabled: colors.grey[400],    // Light grey for disabled text
    },
    divider: colors.grey[300],       // Light grey for dividers
  },
  typography: {
    fontFamily: 'var(--font-inter), "Roboto", "Helvetica", "Arial", sans-serif',
    // Material Design 3 type scale adapted for minimalism
    h1: {
      fontSize: '3.5rem',      // 56px - Display Large
      fontWeight: 400,
      lineHeight: 1.2,
      letterSpacing: '-0.02em',
    },
    h2: {
      fontSize: '2.75rem',     // 44px - Display Medium
      fontWeight: 400,
      lineHeight: 1.2,
      letterSpacing: '-0.01em',
    },
    h3: {
      fontSize: '2.25rem',     // 36px - Display Small
      fontWeight: 400,
      lineHeight: 1.3,
      letterSpacing: '0em',
    },
    h4: {
      fontSize: '2rem',        // 32px - Headline Large
      fontWeight: 500,
      lineHeight: 1.3,
      letterSpacing: '0em',
    },
    h5: {
      fontSize: '1.5rem',      // 24px - Headline Medium
      fontWeight: 500,
      lineHeight: 1.4,
      letterSpacing: '0em',
    },
    h6: {
      fontSize: '1.25rem',     // 20px - Headline Small
      fontWeight: 500,
      lineHeight: 1.4,
      letterSpacing: '0em',
    },
    subtitle1: {
      fontSize: '1rem',        // 16px - Title Large
      fontWeight: 500,
      lineHeight: 1.5,
      letterSpacing: '0.01em',
    },
    subtitle2: {
      fontSize: '0.875rem',    // 14px - Title Medium
      fontWeight: 500,
      lineHeight: 1.5,
      letterSpacing: '0.01em',
    },
    body1: {
      fontSize: '1rem',        // 16px - Body Large
      fontWeight: 400,
      lineHeight: 1.6,
      letterSpacing: '0.01em',
    },
    body2: {
      fontSize: '0.875rem',    // 14px - Body Medium
      fontWeight: 400,
      lineHeight: 1.6,
      letterSpacing: '0.01em',
    },
    caption: {
      fontSize: '0.75rem',     // 12px - Body Small
      fontWeight: 400,
      lineHeight: 1.5,
      letterSpacing: '0.02em',
    },
    button: {
      fontSize: '0.875rem',    // 14px - Label Large
      fontWeight: 500,
      lineHeight: 1.4,
      letterSpacing: '0.02em',
      textTransform: 'none',   // Minimalistic - no uppercase
    },
  },
  spacing: 8, // 8dp grid system from Material Design
  shape: {
    borderRadius: 8, // Slightly rounded corners for modern look
  },
  components: {
    // Customize MUI components for minimalistic aesthetic
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 500,
          boxShadow: 'none',
          '&:hover': {
            boxShadow: 'none',
          },
        },
        contained: {
          '&:hover': {
            boxShadow: '0px 2px 4px rgba(0, 0, 0, 0.1)',
          },
        },
        outlined: {
          borderWidth: '1px',
          '&:hover': {
            borderWidth: '1px',
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.08), 0px 1px 2px rgba(0, 0, 0, 0.12)',
          '&:hover': {
            boxShadow: '0px 2px 6px rgba(0, 0, 0, 0.12), 0px 2px 4px rgba(0, 0, 0, 0.16)',
          },
          transition: 'box-shadow 0.2s ease-in-out',
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: 8,
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          boxShadow: '0px 1px 3px rgba(0, 0, 0, 0.08)',
        },
      },
    },
    MuiTypography: {
      styleOverrides: {
        root: {
          color: colors.grey[800],
        },
      },
    },
  },
});

export default theme;
