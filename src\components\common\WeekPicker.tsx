"use client";

import React from "react";
import WeekPickerForm from "@/components/forms/WeekPicker";

interface WeekPickerProps {
  label?: string;
  value?: string | null;
  onChange: (value: string | null) => void;
  disabled?: boolean;
  required?: boolean;
  error?: boolean;
  helperText?: string;
  fullWidth?: boolean;
  size?: "small" | "medium";
  sx?: any;
}

const WeekPicker: React.FC<WeekPickerProps> = (props) => {
  return <WeekPickerForm {...props} />;
};

export default WeekPicker;
