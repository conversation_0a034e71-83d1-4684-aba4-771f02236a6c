import apiClient from "./apiClient";
import {
  PriorityLevel,
  CreatePriorityLevelData,
  UpdatePriorityLevelData,
  PriorityLevelError,
  PaginatedPriorityLevelsResponse,
} from "@/lib/types/priorities";
import { API_ENDPOINTS } from "@/lib/utils/constants";
import { AxiosError } from "axios";

/**
 * Parse API errors into a consistent format
 */
const parseApiError = (error: AxiosError): PriorityLevelError => {
  console.error("PrioritiesService: API Error:", error);

  if (error.response?.data) {
    const data = error.response.data as any;

    // Handle validation errors with field details
    if (data.errors || data.detail || data.non_field_errors) {
      return {
        message: data.detail || "Validation error occurred",
        details: data.errors || data,
      };
    }

    // Handle simple error messages
    if (data.message || data.detail || data.error) {
      return { message: data.message || data.detail || data.error };
    }
  }

  // Default error message
  return {
    message: error.response?.status === 401 ? "Unauthorized access" : "An unexpected error occurred. Please try again.",
  };
};

export const prioritiesService = {
  /**
   * Get all priority levels for the authenticated user
   */
  async getPriorityLevels(): Promise<PriorityLevel[]> {
    try {
      const response = await apiClient.get<PaginatedPriorityLevelsResponse | PriorityLevel[]>(API_ENDPOINTS.WORK_ITEMS.PRIORITY_LEVELS);

      console.log("Priority levels API response:", response.data);

      // Handle both paginated and direct array responses
      let priorityLevels: PriorityLevel[];

      if (Array.isArray(response.data)) {
        // Direct array response
        priorityLevels = response.data;
      } else if (response.data && "results" in response.data) {
        // Paginated response
        priorityLevels = response.data.results;
      } else {
        console.warn("Unexpected priority levels response format:", response.data);
        priorityLevels = [];
      }

      // Normalize the data: map sort_order to display_order for frontend consistency
      const normalizedPriorityLevels = priorityLevels.map((level: any) => ({
        ...level,
        display_order: level.display_order || level.sort_order || 0,
        sort_order: level.sort_order || level.display_order || 0,
      }));

      console.log("Processed priority levels:", normalizedPriorityLevels);
      return normalizedPriorityLevels;
    } catch (error) {
      console.error("Error fetching priority levels:", error);
      throw parseApiError(error as AxiosError);
    }
  },

  /**
   * Get a specific priority level by ID
   */
  async getPriorityLevel(id: string): Promise<PriorityLevel> {
    try {
      const response = await apiClient.get<PriorityLevel>(`${API_ENDPOINTS.WORK_ITEMS.PRIORITY_LEVELS}${id}/`);
      return response.data;
    } catch (error) {
      throw parseApiError(error as AxiosError);
    }
  },

  /**
   * Create a new priority level
   */
  async createPriorityLevel(data: CreatePriorityLevelData): Promise<PriorityLevel> {
    try {
      const response = await apiClient.post<PriorityLevel>(API_ENDPOINTS.WORK_ITEMS.PRIORITY_LEVELS, data);
      return response.data;
    } catch (error) {
      throw parseApiError(error as AxiosError);
    }
  },

  /**
   * Update an existing priority level
   */
  async updatePriorityLevel(id: string, data: UpdatePriorityLevelData): Promise<PriorityLevel> {
    try {
      const response = await apiClient.patch<PriorityLevel>(`${API_ENDPOINTS.WORK_ITEMS.PRIORITY_LEVELS}${id}/`, data);
      return response.data;
    } catch (error) {
      throw parseApiError(error as AxiosError);
    }
  },

  /**
   * Delete a priority level
   */
  async deletePriorityLevel(id: string): Promise<void> {
    try {
      await apiClient.delete(`${API_ENDPOINTS.WORK_ITEMS.PRIORITY_LEVELS}${id}/`);
    } catch (error) {
      throw parseApiError(error as AxiosError);
    }
  },
};
