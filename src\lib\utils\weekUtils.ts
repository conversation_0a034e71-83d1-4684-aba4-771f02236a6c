/**
 * Week utility functions for WEEK custom field type
 * Handles conversion between YYYY-WNN format and user-friendly display format
 * Respects user's week_starts_on preference (Sunday or Monday)
 */

import {
  startOfWeek,
  endOfWeek,
  getISOWeek,
  getYear,
  format,
  parseISO,
  eachDayOfInterval,
  isSameWeek,
  addWeeks,
  subWeeks,
  startOfMonth,
  endOfMonth,
  eachWeekOfInterval,
} from "date-fns";

export type WeekStartDay = "Sunday" | "Monday";

/**
 * Convert week start day preference to date-fns weekStartsOn option
 */
export const getWeekStartsOnOption = (weekStartsOn: WeekStartDay): 0 | 1 => {
  return weekStartsOn === "Sunday" ? 0 : 1;
};

/**
 * Convert YYYY-WNN format to Date object (start of week)
 * @param weekString - Week string in YYYY-WNN format (e.g., "2025-W25")
 * @param weekStartsOn - User's week start preference
 * @returns Date object representing the start of the week
 */
export const weekStringToDate = (weekString: string, weekStartsOn: WeekStartDay = "Sunday"): Date | null => {
  if (!weekString || !weekString.match(/^\d{4}-W\d{2}$/)) {
    return null;
  }

  try {
    const [yearStr, weekStr] = weekString.split("-W");
    const year = parseInt(yearStr, 10);
    const week = parseInt(weekStr, 10);

    // Create a date for January 1st of the year
    const jan1 = new Date(year, 0, 1);

    // Find the first week of the year
    const firstWeekStart = startOfWeek(jan1, { weekStartsOn: getWeekStartsOnOption(weekStartsOn) });

    // Calculate the target week by adding weeks
    const targetWeekStart = addWeeks(firstWeekStart, week - 1);

    return targetWeekStart;
  } catch (error) {
    console.warn("Error parsing week string:", error, weekString);
    return null;
  }
};

/**
 * Convert Date object to YYYY-WNN format
 * @param date - Date object
 * @param weekStartsOn - User's week start preference
 * @returns Week string in YYYY-WNN format
 */
export const dateToWeekString = (date: Date, weekStartsOn: WeekStartDay = "Sunday"): string => {
  if (!date || isNaN(date.getTime())) {
    return "";
  }

  try {
    const year = getYear(date);
    const weekStart = startOfWeek(date, { weekStartsOn: getWeekStartsOnOption(weekStartsOn) });

    // Calculate week number based on the year and week start preference
    const jan1 = new Date(year, 0, 1);
    const firstWeekStart = startOfWeek(jan1, { weekStartsOn: getWeekStartsOnOption(weekStartsOn) });

    // Calculate the difference in weeks
    const weeksDiff = Math.floor((weekStart.getTime() - firstWeekStart.getTime()) / (7 * 24 * 60 * 60 * 1000));
    const weekNumber = weeksDiff + 1;

    // Handle edge cases for weeks that span years
    if (weekNumber <= 0) {
      // This week belongs to the previous year
      const prevYear = year - 1;
      const prevYearLastWeek = getLastWeekOfYear(prevYear, weekStartsOn);
      return `${prevYear}-W${prevYearLastWeek.toString().padStart(2, "0")}`;
    }

    const maxWeeksInYear = getWeeksInYear(year, weekStartsOn);
    if (weekNumber > maxWeeksInYear) {
      // This week belongs to the next year
      return `${year + 1}-W01`;
    }

    return `${year}-W${weekNumber.toString().padStart(2, "0")}`;
  } catch (error) {
    console.warn("Error converting date to week string:", error, date);
    return "";
  }
};

/**
 * Get the number of weeks in a year
 */
const getWeeksInYear = (year: number, weekStartsOn: WeekStartDay): number => {
  const dec31 = new Date(year, 11, 31);
  const lastWeekStart = startOfWeek(dec31, { weekStartsOn: getWeekStartsOnOption(weekStartsOn) });
  const jan1 = new Date(year, 0, 1);
  const firstWeekStart = startOfWeek(jan1, { weekStartsOn: getWeekStartsOnOption(weekStartsOn) });

  const weeksDiff = Math.floor((lastWeekStart.getTime() - firstWeekStart.getTime()) / (7 * 24 * 60 * 60 * 1000));
  return weeksDiff + 1;
};

/**
 * Get the last week number of a year
 */
const getLastWeekOfYear = (year: number, weekStartsOn: WeekStartDay): number => {
  return getWeeksInYear(year, weekStartsOn);
};

/**
 * Format week string for user-friendly display
 * @param weekString - Week string in YYYY-WNN format
 * @param weekStartsOn - User's week start preference
 * @returns Formatted string like "W25: 15 Jun - 21 Jun"
 */
export const formatWeekForDisplay = (weekString: string, weekStartsOn: WeekStartDay = "Sunday"): string => {
  if (!weekString) return "";

  const weekStart = weekStringToDate(weekString, weekStartsOn);
  if (!weekStart) return weekString; // Fallback to original string

  const weekEnd = endOfWeek(weekStart, { weekStartsOn: getWeekStartsOnOption(weekStartsOn) });

  // Extract week number from the string
  const weekMatch = weekString.match(/W(\d+)/);
  const weekNumber = weekMatch ? weekMatch[1] : "??";

  // Format the dates using the shorter format (d MMM)
  const startFormatted = format(weekStart, "d MMM");
  const endFormatted = format(weekEnd, "d MMM");

  return `W${weekNumber}: ${startFormatted} - ${endFormatted}`;
};

/**
 * Get all days in a week containing the given date
 * @param date - Any date within the week
 * @param weekStartsOn - User's week start preference
 * @returns Array of Date objects for each day in the week
 */
export const getWeekDays = (date: Date, weekStartsOn: WeekStartDay = "Sunday"): Date[] => {
  const weekStart = startOfWeek(date, { weekStartsOn: getWeekStartsOnOption(weekStartsOn) });
  const weekEnd = endOfWeek(date, { weekStartsOn: getWeekStartsOnOption(weekStartsOn) });

  return eachDayOfInterval({ start: weekStart, end: weekEnd });
};

/**
 * Check if a date is in the same week as another date
 * @param date1 - First date
 * @param date2 - Second date
 * @param weekStartsOn - User's week start preference
 * @returns Boolean indicating if dates are in the same week
 */
export const isSameWeekCustom = (date1: Date, date2: Date, weekStartsOn: WeekStartDay = "Sunday"): boolean => {
  return isSameWeek(date1, date2, { weekStartsOn: getWeekStartsOnOption(weekStartsOn) });
};

/**
 * Validate if a string is in YYYY-WNN format
 * @param weekString - String to validate
 * @returns Boolean indicating if the format is valid
 */
export const isValidWeekFormat = (weekString: string): boolean => {
  if (!weekString) return false;

  const weekRegex = /^\d{4}-W(0[1-9]|[1-4][0-9]|5[0-3])$/;
  return weekRegex.test(weekString);
};

/**
 * Get the current week in YYYY-WNN format
 * @param weekStartsOn - User's week start preference
 * @returns Current week string
 */
export const getCurrentWeek = (weekStartsOn: WeekStartDay = "Sunday"): string => {
  return dateToWeekString(new Date(), weekStartsOn);
};

/**
 * Get all weeks in a month for calendar display
 * @param date - Any date within the month
 * @param weekStartsOn - User's week start preference
 * @returns Array of week start dates
 */
export const getWeeksInMonth = (date: Date, weekStartsOn: WeekStartDay = "Sunday"): Date[] => {
  const monthStart = startOfMonth(date);
  const monthEnd = endOfMonth(date);

  return eachWeekOfInterval({ start: monthStart, end: monthEnd }, { weekStartsOn: getWeekStartsOnOption(weekStartsOn) });
};

/**
 * Get the week number for a given date
 * @param date - Date to get week number for
 * @param weekStartsOn - User's week start preference
 * @returns Week number (1-53)
 */
export const getWeekNumber = (date: Date, weekStartsOn: WeekStartDay = "Sunday"): number => {
  if (!date || isNaN(date.getTime())) {
    return 1;
  }

  try {
    const year = getYear(date);
    const weekStart = startOfWeek(date, { weekStartsOn: getWeekStartsOnOption(weekStartsOn) });

    // Calculate week number based on the year and week start preference
    const jan1 = new Date(year, 0, 1);
    const firstWeekStart = startOfWeek(jan1, { weekStartsOn: getWeekStartsOnOption(weekStartsOn) });

    // Calculate the difference in weeks
    const weeksDiff = Math.floor((weekStart.getTime() - firstWeekStart.getTime()) / (7 * 24 * 60 * 60 * 1000));
    const weekNumber = weeksDiff + 1;

    // Handle edge cases for weeks that span years
    if (weekNumber <= 0) {
      // This week belongs to the previous year
      const prevYear = year - 1;
      return getLastWeekOfYear(prevYear, weekStartsOn);
    }

    const maxWeeksInYear = getWeeksInYear(year, weekStartsOn);
    if (weekNumber > maxWeeksInYear) {
      // This week belongs to the next year
      return 1;
    }

    return weekNumber;
  } catch (error) {
    console.warn("Error calculating week number:", error, date);
    return 1;
  }
};

/**
 * Check if a date is in the current week
 * @param date - Date to check
 * @param weekStartsOn - User's week start preference
 * @returns Boolean indicating if date is in current week
 */
export const isCurrentWeek = (date: Date, weekStartsOn: WeekStartDay = "Sunday"): boolean => {
  const today = new Date();
  return isSameWeekCustom(date, today, weekStartsOn);
};
