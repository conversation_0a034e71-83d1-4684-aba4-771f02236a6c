/**
 * Custom Fields Service Tests
 * Basic test structure for the custom fields API service
 */

import { customFieldsService } from "../customFieldsService";
import { CreateCustomFieldDefinitionData, CustomFieldDefinition } from "@/lib/types/customFields";

// Mock the API client
jest.mock("../apiClient", () => ({
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  patch: jest.fn(),
  delete: jest.fn(),
}));

import apiClient from "../apiClient";

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe("customFieldsService", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("getCustomFieldDefinitions", () => {
    it("should fetch custom field definitions successfully", async () => {
      const mockDefinitions: CustomFieldDefinition[] = [
        {
          id: "123e4567-e89b-12d3-a456-************",
          name: "Priority Level",
          field_type: "SINGLE_SELECT",
          target_model: "PROJECT",
          is_required: false,
          sort_order: 0,
          choice_options: [],
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-01T00:00:00Z",
          user: "user-uuid",
        },
      ];

      mockApiClient.get.mockResolvedValue({ data: mockDefinitions });

      const result = await customFieldsService.getCustomFieldDefinitions();

      expect(mockApiClient.get).toHaveBeenCalledWith("/workitems/custom-fields/");
      expect(result).toEqual(mockDefinitions);
    });

    it("should handle paginated response", async () => {
      const mockDefinitions: CustomFieldDefinition[] = [
        {
          id: "123e4567-e89b-12d3-a456-************",
          name: "Priority Level",
          field_type: "SINGLE_SELECT",
          target_model: "PROJECT",
          is_required: false,
          sort_order: 0,
          choice_options: [],
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-01T00:00:00Z",
          user: "user-uuid",
        },
      ];

      const mockPaginatedResponse = {
        count: 1,
        next: null,
        previous: null,
        results: mockDefinitions,
      };

      mockApiClient.get.mockResolvedValue({ data: mockPaginatedResponse });

      const result = await customFieldsService.getCustomFieldDefinitions();

      expect(result).toEqual(mockDefinitions);
    });

    it("should filter by target model when provided", async () => {
      const mockDefinitions: CustomFieldDefinition[] = [];
      mockApiClient.get.mockResolvedValue({ data: mockDefinitions });

      await customFieldsService.getCustomFieldDefinitions("PROJECT");

      expect(mockApiClient.get).toHaveBeenCalledWith("/workitems/custom-fields/?target_model=PROJECT");
    });
  });

  describe("createCustomFieldDefinition", () => {
    it("should create a custom field definition successfully", async () => {
      const createData: CreateCustomFieldDefinitionData = {
        name: "Test Field",
        field_type: "TEXT",
        target_model: "PROJECT",
        is_required: false,
      };

      const mockCreatedDefinition: CustomFieldDefinition = {
        id: "123e4567-e89b-12d3-a456-************",
        name: "Test Field",
        field_type: "TEXT",
        target_model: "PROJECT",
        is_required: false,
        sort_order: 0,
        choice_options: [],
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        user: "user-uuid",
      };

      mockApiClient.post.mockResolvedValue({ data: mockCreatedDefinition });

      const result = await customFieldsService.createCustomFieldDefinition(createData);

      expect(mockApiClient.post).toHaveBeenCalledWith("/workitems/custom-fields/", createData);
      expect(result).toEqual(mockCreatedDefinition);
    });
  });

  describe("updateCustomFieldDefinition", () => {
    it("should update a custom field definition successfully", async () => {
      // According to USING_CUSTOM_APIS.md, PUT requests require ALL required fields
      const updateData = {
        name: "Updated Field Name",
        field_type: "TEXT" as const,
        target_model: "PROJECT" as const,
        is_required: true,
        sort_order: 0,
        choice_options: [],
      };

      const mockUpdatedDefinition: CustomFieldDefinition = {
        id: "123e4567-e89b-12d3-a456-************",
        name: "Updated Field Name",
        field_type: "TEXT",
        target_model: "PROJECT",
        is_required: true,
        sort_order: 0,
        choice_options: [],
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        user: "user-uuid",
      };

      mockApiClient.put.mockResolvedValue({ data: mockUpdatedDefinition });

      const result = await customFieldsService.updateCustomFieldDefinition("123e4567-e89b-12d3-a456-************", updateData);

      expect(mockApiClient.put).toHaveBeenCalledWith("/workitems/custom-fields/123e4567-e89b-12d3-a456-************/", updateData);
      expect(result).toEqual(mockUpdatedDefinition);
    });
  });

  describe("deleteCustomFieldDefinition", () => {
    it("should delete a custom field definition successfully", async () => {
      mockApiClient.delete.mockResolvedValue({ data: null });

      await customFieldsService.deleteCustomFieldDefinition("123e4567-e89b-12d3-a456-************");

      expect(mockApiClient.delete).toHaveBeenCalledWith("/workitems/custom-fields/123e4567-e89b-12d3-a456-************/");
    });
  });

  describe("getCustomFieldDefinitionsGrouped", () => {
    it("should group custom field definitions by target model", async () => {
      const mockDefinitions: CustomFieldDefinition[] = [
        {
          id: "1",
          name: "Project Priority",
          field_type: "SINGLE_SELECT",
          target_model: "PROJECT",
          is_required: false,
          sort_order: 0,
          choice_options: [],
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-01T00:00:00Z",
          user: "user-uuid",
        },
        {
          id: "2",
          name: "Outcome Status",
          field_type: "SINGLE_SELECT",
          target_model: "OUTCOME",
          is_required: true,
          sort_order: 1,
          choice_options: [],
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-01T00:00:00Z",
          user: "user-uuid",
        },
      ];

      mockApiClient.get.mockResolvedValue({ data: mockDefinitions });

      const result = await customFieldsService.getCustomFieldDefinitionsGrouped();

      expect(result).toEqual({
        PROJECT: [mockDefinitions[0]],
        OUTCOME: [mockDefinitions[1]],
        WEEKLY_PLAN_FOCUS: [],
      });
    });
  });
});

// Export for potential use in other test files
export const mockCustomFieldDefinition: CustomFieldDefinition = {
  id: "123e4567-e89b-12d3-a456-************",
  name: "Test Field",
  field_type: "TEXT",
  target_model: "PROJECT",
  is_required: false,
  sort_order: 0,
  choice_options: [],
  created_at: "2024-01-01T00:00:00Z",
  updated_at: "2024-01-01T00:00:00Z",
  user: "user-uuid",
};
