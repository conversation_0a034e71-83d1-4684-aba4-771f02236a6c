import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database/prisma';
import { verifyToken, extractTokenFromHeader } from '@/lib/auth/jwt';
import { validateUpdateOutcome, ValidationError, OutcomeUpdate } from '@/lib/validation/outcomes';
import { resolveCustomFields, processCustomFieldInputs } from '@/lib/utils/customFieldResolver';

// Helper function to authenticate request
async function authenticateRequest(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  const token = extractTokenFromHeader(authHeader);
  
  if (!token) {
    return { error: NextResponse.json({ message: 'Authentication required' }, { status: 401 }) };
  }
  
  const payload = verifyToken(token);
  if (!payload) {
    return { error: NextResponse.json({ message: 'Invalid or expired token' }, { status: 401 }) };
  }
  
  return { userId: payload.userId };
}

// Helper function to create validation error response
function createValidationErrorResponse(errors: ValidationError[]) {
  const errorMap: Record<string, string[]> = {};
  errors.forEach(error => {
    if (!errorMap[error.field]) errorMap[error.field] = [];
    errorMap[error.field].push(error.message);
  });
  
  return NextResponse.json({
    message: 'Validation failed',
    errors: errorMap
  }, { status: 400 });
}

// Helper function to extract legacy priority from custom fields
function extractLegacyPriority(resolvedCustomFields: any[]): { priority_level: string | null, priority_level_name: string | null } {
  const priorityField = resolvedCustomFields.find(field => 
    field.name.toLowerCase().includes('priority') && 
    field.field_type === 'SINGLE_SELECT' &&
    field.value
  );
  
  if (priorityField && priorityField.value) {
    return {
      priority_level: priorityField.value.id || null,
      priority_level_name: priorityField.value.value || null
    };
  }
  
  return {
    priority_level: null,
    priority_level_name: null
  };
}

// Helper function to find and verify ownership of outcome
async function findAndVerifyOutcome(id: string, userId: string) {
  const outcome = await prisma.workitems_outcome.findUnique({
    where: { id },
    include: {
      project: {
        select: {
          id: true,
          name: true,
          life_aspect: {
            select: {
              id: true,
              name: true,
              color: true
            }
          }
        }
      }
    }
  });
  
  if (!outcome) {
    return { error: NextResponse.json({ message: 'Outcome not found' }, { status: 404 }) };
  }
  
  if (outcome.user_id !== userId) {
    return { error: NextResponse.json({ message: 'Access forbidden' }, { status: 403 }) };
  }
  
  return { outcome };
}

// GET /api/workitems/outcomes/[id] - Get single outcome
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`GET /api/workitems/outcomes/${params.id} called`);
    
    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;
    
    // Find and verify outcome
    const result = await findAndVerifyOutcome(params.id, auth.userId);
    if (result.error) return result.error;
    
    // Resolve custom fields
    const resolvedCustomFields = await resolveCustomFields(
      result.outcome.custom_field_values as Record<string, any>,
      'OUTCOME',
      auth.userId
    );
    
    // Extract legacy priority for hybrid support
    const legacyPriority = extractLegacyPriority(resolvedCustomFields);
    
    const outcomeWithCustomFields = {
      ...result.outcome,
      resolved_custom_fields: resolvedCustomFields,
      priority_level: result.outcome.priority_level || legacyPriority.priority_level,
      priority_level_name: result.outcome.priority_level_name || legacyPriority.priority_level_name
    };
    
    console.log('Found outcome:', result.outcome.id);
    
    return NextResponse.json(outcomeWithCustomFields);
    
  } catch (error) {
    console.error('Error fetching outcome:', error);
    return NextResponse.json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// PUT /api/workitems/outcomes/[id] - Full update outcome
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  // For now, PUT behaves the same as PATCH
  return PATCH(request, { params });
}

// PATCH /api/workitems/outcomes/[id] - Update outcome
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`PATCH /api/workitems/outcomes/${params.id} called`);
    
    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;
    
    // Find and verify outcome
    const result = await findAndVerifyOutcome(params.id, auth.userId);
    if (result.error) return result.error;
    
    // Parse request body
    const body = await request.json() as OutcomeUpdate;
    console.log('Request body:', { ...body, custom_field_inputs: body.custom_field_inputs?.length || 0 });
    
    // Validate input
    const validationErrors = validateUpdateOutcome(body);
    if (validationErrors.length > 0) {
      return createValidationErrorResponse(validationErrors);
    }
    
    // Prepare update data
    const updateData: any = {};
    
    if (body.name !== undefined) updateData.name = body.name;
    if (body.description !== undefined) updateData.description = body.description;
    if (body.sort_order !== undefined) updateData.sort_order = body.sort_order;
    
    // Verify project exists and belongs to user (if being updated)
    if (body.project_id !== undefined) {
      const project = await prisma.workitems_project.findFirst({
        where: {
          id: body.project_id,
          user_id: auth.userId
        }
      });
      
      if (!project) {
        return NextResponse.json({
          message: 'Project not found or does not belong to user'
        }, { status: 400 });
      }
      
      updateData.project_id = body.project_id;
    }
    
    // Process custom field inputs
    if (body.custom_field_inputs !== undefined) {
      try {
        const existingValues = result.outcome.custom_field_values as Record<string, any>;
        const updatedValues = await processCustomFieldInputs(
          body.custom_field_inputs,
          'OUTCOME',
          auth.userId,
          existingValues
        );
        updateData.custom_field_values = updatedValues;
      } catch (error) {
        return NextResponse.json({
          message: 'Custom field validation failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        }, { status: 400 });
      }
    }
    
    // Update outcome
    const updatedOutcome = await prisma.workitems_outcome.update({
      where: { id: params.id },
      data: updateData,
      include: {
        project: {
          select: {
            id: true,
            name: true,
            life_aspect: {
              select: {
                id: true,
                name: true,
                color: true
              }
            }
          }
        }
      }
    });
    
    // Resolve custom fields
    const resolvedCustomFields = await resolveCustomFields(
      updatedOutcome.custom_field_values as Record<string, any>,
      'OUTCOME',
      auth.userId
    );
    
    // Extract legacy priority for hybrid support
    const legacyPriority = extractLegacyPriority(resolvedCustomFields);
    
    const outcomeWithCustomFields = {
      ...updatedOutcome,
      resolved_custom_fields: resolvedCustomFields,
      priority_level: updatedOutcome.priority_level || legacyPriority.priority_level,
      priority_level_name: updatedOutcome.priority_level_name || legacyPriority.priority_level_name
    };
    
    console.log('Updated outcome:', updatedOutcome.id);
    
    return NextResponse.json(outcomeWithCustomFields);
    
  } catch (error) {
    console.error('Error updating outcome:', error);
    return NextResponse.json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// DELETE /api/workitems/outcomes/[id] - Delete outcome
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log(`DELETE /api/workitems/outcomes/${params.id} called`);
    
    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;
    
    // Find and verify outcome
    const result = await findAndVerifyOutcome(params.id, auth.userId);
    if (result.error) return result.error;
    
    // Delete outcome
    await prisma.workitems_outcome.delete({
      where: { id: params.id }
    });
    
    console.log('Deleted outcome:', params.id);
    
    return NextResponse.json({ message: 'Outcome deleted successfully' });
    
  } catch (error) {
    console.error('Error deleting outcome:', error);
    return NextResponse.json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
