import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/database/prisma';
import { verifyToken, extractTokenFromHeader } from '@/lib/auth/jwt';

// Helper function to authenticate request
async function authenticateRequest(request: NextRequest) {
  const authHeader = request.headers.get('authorization');
  const token = extractTokenFromHeader(authHeader);
  
  if (!token) {
    return { error: NextResponse.json({ message: 'Authentication required' }, { status: 401 }) };
  }
  
  const payload = verifyToken(token);
  if (!payload) {
    return { error: NextResponse.json({ message: 'Invalid or expired token' }, { status: 401 }) };
  }
  
  return { userId: payload.userId };
}

interface LifeAspectInput {
  name: string;
  color?: string;
  sort_order?: number;
}

// GET /api/workitems/life-aspects/ - List life aspects
export async function GET(request: NextRequest) {
  try {
    console.log('GET /api/workitems/life-aspects/ called');
    
    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;
    
    // Fetch life aspects
    const lifeAspects = await prisma.workitems_life_aspect.findMany({
      where: { user_id: auth.userId },
      orderBy: [
        { sort_order: 'asc' },
        { created_at: 'asc' }
      ]
    });
    
    console.log(`Found ${lifeAspects.length} life aspects for user ${auth.userId}`);
    
    return NextResponse.json(lifeAspects);
    
  } catch (error) {
    console.error('Error fetching life aspects:', error);
    return NextResponse.json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// POST /api/workitems/life-aspects/ - Create life aspect
export async function POST(request: NextRequest) {
  try {
    console.log('POST /api/workitems/life-aspects/ called');
    
    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;
    
    // Parse request body
    const body = await request.json() as LifeAspectInput;
    console.log('Request body:', body);
    
    // Basic validation
    if (!body.name || typeof body.name !== 'string' || body.name.trim().length === 0) {
      return NextResponse.json({
        message: 'Life aspect name is required'
      }, { status: 400 });
    }
    
    if (body.name.length > 100) {
      return NextResponse.json({
        message: 'Life aspect name must be less than 100 characters'
      }, { status: 400 });
    }
    
    // Check for duplicate name
    const existingLifeAspect = await prisma.workitems_life_aspect.findFirst({
      where: {
        user_id: auth.userId,
        name: body.name
      }
    });
    
    if (existingLifeAspect) {
      return NextResponse.json({
        message: 'A life aspect with this name already exists'
      }, { status: 400 });
    }
    
    // Create life aspect
    const lifeAspect = await prisma.workitems_life_aspect.create({
      data: {
        user_id: auth.userId,
        name: body.name,
        color: body.color || '#3498db',
        sort_order: body.sort_order || 0
      }
    });
    
    console.log('Created life aspect:', lifeAspect.id);
    
    return NextResponse.json(lifeAspect, { status: 201 });
    
  } catch (error) {
    console.error('Error creating life aspect:', error);
    return NextResponse.json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
