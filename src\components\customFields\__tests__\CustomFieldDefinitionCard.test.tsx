/**
 * CustomFieldDefinitionCard Component Tests
 * Tests for the custom field definition card component
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import CustomFieldDefinitionCard from '../CustomFieldDefinitionCard';
import { CustomFieldDefinition } from '@/lib/types/customFields';
import { theme } from '@/lib/theme/theme';

// Mock definition for testing
const mockDefinition: CustomFieldDefinition = {
  id: '123e4567-e89b-12d3-a456-426614174000',
  name: 'Priority Level',
  field_type: 'SINGLE_SELECT',
  target_model: 'PROJECT',
  is_required: false,
  sort_order: 0,
  choice_options: [
    {
      id: 'choice-1',
      value: 'High',
      color: '#d32f2f',
      sort_order: 0,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 'choice-2',
      value: 'Medium',
      color: '#f57c00',
      sort_order: 1,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 'choice-3',
      value: 'Low',
      color: '#388e3c',
      sort_order: 2,
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
    },
  ],
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  user: 'user-uuid',
};

// Mock functions
const mockOnEdit = jest.fn();
const mockOnDelete = jest.fn();
const mockOnToggleRequired = jest.fn();

// Wrapper component with theme
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider theme={theme}>{children}</ThemeProvider>
);

describe('CustomFieldDefinitionCard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders field name correctly', () => {
    render(
      <TestWrapper>
        <CustomFieldDefinitionCard
          definition={mockDefinition}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onToggleRequired={mockOnToggleRequired}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Priority Level')).toBeInTheDocument();
  });

  it('displays field type chip', () => {
    render(
      <TestWrapper>
        <CustomFieldDefinitionCard
          definition={mockDefinition}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onToggleRequired={mockOnToggleRequired}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Dropdown')).toBeInTheDocument();
  });

  it('shows choice options for SELECT fields', () => {
    render(
      <TestWrapper>
        <CustomFieldDefinitionCard
          definition={mockDefinition}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onToggleRequired={mockOnToggleRequired}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Options:')).toBeInTheDocument();
    expect(screen.getByText('High')).toBeInTheDocument();
    expect(screen.getByText('Medium')).toBeInTheDocument();
    expect(screen.getByText('Low')).toBeInTheDocument();
  });

  it('displays required toggle switch', () => {
    render(
      <TestWrapper>
        <CustomFieldDefinitionCard
          definition={mockDefinition}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onToggleRequired={mockOnToggleRequired}
        />
      </TestWrapper>
    );

    const requiredSwitch = screen.getByRole('checkbox');
    expect(requiredSwitch).toBeInTheDocument();
    expect(requiredSwitch).not.toBeChecked();
  });

  it('calls onEdit when card is clicked', () => {
    render(
      <TestWrapper>
        <CustomFieldDefinitionCard
          definition={mockDefinition}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onToggleRequired={mockOnToggleRequired}
        />
      </TestWrapper>
    );

    const card = screen.getByRole('button', { name: /priority level/i });
    fireEvent.click(card);

    expect(mockOnEdit).toHaveBeenCalledWith(mockDefinition);
  });

  it('calls onToggleRequired when switch is toggled', () => {
    render(
      <TestWrapper>
        <CustomFieldDefinitionCard
          definition={mockDefinition}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onToggleRequired={mockOnToggleRequired}
        />
      </TestWrapper>
    );

    const requiredSwitch = screen.getByRole('checkbox');
    fireEvent.click(requiredSwitch);

    expect(mockOnToggleRequired).toHaveBeenCalledWith(mockDefinition);
  });

  it('renders correctly for non-SELECT field types', () => {
    const textFieldDefinition: CustomFieldDefinition = {
      ...mockDefinition,
      field_type: 'TEXT',
      choice_options: [],
    };

    render(
      <TestWrapper>
        <CustomFieldDefinitionCard
          definition={textFieldDefinition}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onToggleRequired={mockOnToggleRequired}
        />
      </TestWrapper>
    );

    expect(screen.getByText('Text Input')).toBeInTheDocument();
    expect(screen.getByText('Single line text')).toBeInTheDocument();
    expect(screen.queryByText('Options:')).not.toBeInTheDocument();
  });

  it('shows required field correctly when is_required is true', () => {
    const requiredDefinition: CustomFieldDefinition = {
      ...mockDefinition,
      is_required: true,
    };

    render(
      <TestWrapper>
        <CustomFieldDefinitionCard
          definition={requiredDefinition}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onToggleRequired={mockOnToggleRequired}
        />
      </TestWrapper>
    );

    const requiredSwitch = screen.getByRole('checkbox');
    expect(requiredSwitch).toBeChecked();
  });

  it('limits choice options display to 4 items', () => {
    const manyChoicesDefinition: CustomFieldDefinition = {
      ...mockDefinition,
      choice_options: [
        ...mockDefinition.choice_options!,
        {
          id: 'choice-4',
          value: 'Urgent',
          color: '#ff0000',
          sort_order: 3,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
        {
          id: 'choice-5',
          value: 'Critical',
          color: '#800000',
          sort_order: 4,
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
      ],
    };

    render(
      <TestWrapper>
        <CustomFieldDefinitionCard
          definition={manyChoicesDefinition}
          onEdit={mockOnEdit}
          onDelete={mockOnDelete}
          onToggleRequired={mockOnToggleRequired}
        />
      </TestWrapper>
    );

    expect(screen.getByText('+1 more')).toBeInTheDocument();
  });
});

// Export mock data for use in other tests
export { mockDefinition, mockOnEdit, mockOnDelete, mockOnToggleRequired };
