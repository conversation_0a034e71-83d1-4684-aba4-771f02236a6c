"use client";

import React, { useState, useEffect, useRef } from "react";
import { TextField, Popover, Box, Typography, IconButton, Grid, Paper, Skeleton, Alert } from "@mui/material";
import {
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  CalendarToday as CalendarIcon,
  Clear as ClearIcon,
} from "@mui/icons-material";
import { format, startOfMonth, endOfMonth, eachDayOfInterval, addMonths, subMonths, isSameMonth, isToday } from "date-fns";
import { userPreferencesService } from "@/lib/api/userPreferencesService";
import {
  formatWeekForDisplay,
  dateToWeekString,
  weekStringToDate,
  getWeekDays,
  isSameWeekCustom,
  WeekStartDay,
  getWeekNumber,
  isCurrentWeek,
} from "@/lib/utils/weekUtils";

interface WeekPickerProps {
  label: string;
  value: string | null; // YYYY-WNN format
  onChange: (value: string | null) => void;
  disabled?: boolean;
  required?: boolean;
  error?: boolean;
  helperText?: string;
  fullWidth?: boolean;
  size?: "small" | "medium";
}

const WeekPicker: React.FC<WeekPickerProps> = ({
  label,
  value,
  onChange,
  disabled = false,
  required = false,
  error = false,
  helperText,
  fullWidth = true,
  size = "small",
}) => {
  // State
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());
  const [weekStartsOn, setWeekStartsOn] = useState<WeekStartDay>("Sunday");
  const [hoveredDate, setHoveredDate] = useState<Date | null>(null);
  const [loading, setLoading] = useState(true);
  const [apiError, setApiError] = useState<string | null>(null);

  const textFieldRef = useRef<HTMLInputElement>(null);

  // Load user preferences on mount
  useEffect(() => {
    loadUserPreferences();
  }, []);

  const loadUserPreferences = async () => {
    try {
      setLoading(true);
      setApiError(null);
      const preferences = await userPreferencesService.getPreferences();
      setWeekStartsOn(preferences.week_starts_on || "Sunday");
    } catch (err) {
      console.error("Error loading user preferences:", err);
      setApiError("Failed to load preferences");
      // Default to Sunday if we can't load preferences
      setWeekStartsOn("Sunday");
    } finally {
      setLoading(false);
    }
  };

  // Handle input field click
  const handleInputClick = (event: React.MouseEvent<HTMLElement>) => {
    if (!disabled) {
      setAnchorEl(event.currentTarget);

      // If there's a current value, navigate to that month
      if (value) {
        const weekDate = weekStringToDate(value, weekStartsOn);
        if (weekDate) {
          setCurrentMonth(weekDate);
        }
      }
    }
  };

  // Handle popover close
  const handleClose = () => {
    setAnchorEl(null);
    setHoveredDate(null);
  };

  // Handle month navigation
  const handlePreviousMonth = () => {
    setCurrentMonth((prev) => subMonths(prev, 1));
  };

  const handleNextMonth = () => {
    setCurrentMonth((prev) => addMonths(prev, 1));
  };

  // Handle day click
  const handleDayClick = (date: Date) => {
    const weekString = dateToWeekString(date, weekStartsOn);
    onChange(weekString);
    handleClose();
  };

  // Handle day hover
  const handleDayHover = (date: Date | null) => {
    setHoveredDate(date);
  };

  // Handle clear button click
  const handleClear = (event: React.MouseEvent<HTMLElement>) => {
    event.stopPropagation(); // Prevent opening the popover
    onChange(null);
  };

  // Get display value
  const displayValue = value ? formatWeekForDisplay(value, weekStartsOn) : "";

  // Get calendar days with proper week start alignment
  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(currentMonth);

  // Calculate the first day to show (start of week containing first day of month)
  const firstDayOfWeek = weekStartsOn === "Sunday" ? 0 : 1;
  const calendarStart = new Date(monthStart);
  while (calendarStart.getDay() !== firstDayOfWeek) {
    calendarStart.setDate(calendarStart.getDate() - 1);
  }

  // Calculate the last day to show (end of week containing last day of month)
  const calendarEnd = new Date(monthEnd);
  const lastDayOfWeek = weekStartsOn === "Sunday" ? 6 : 0;
  while (calendarEnd.getDay() !== lastDayOfWeek) {
    calendarEnd.setDate(calendarEnd.getDate() + 1);
  }

  // Get all days to display in the calendar (only current month days)
  const currentMonthDays = eachDayOfInterval({
    start: monthStart,
    end: monthEnd,
  });

  // Get selected week dates
  const selectedWeekDates = value ? getWeekDays(weekStringToDate(value, weekStartsOn) || new Date(), weekStartsOn) : [];

  // Get hovered week dates
  const hoveredWeekDates = hoveredDate ? getWeekDays(hoveredDate, weekStartsOn) : [];

  // Check if a date is selected
  const isDateSelected = (date: Date) => {
    return selectedWeekDates.some((selectedDate) => selectedDate.toDateString() === date.toDateString());
  };

  // Check if a date is hovered
  const isDateHovered = (date: Date) => {
    return hoveredWeekDates.some((hoveredDate) => hoveredDate.toDateString() === date.toDateString());
  };

  const open = Boolean(anchorEl);

  return (
    <>
      <TextField
        ref={textFieldRef}
        label={label}
        value={displayValue}
        onClick={handleInputClick}
        disabled={disabled}
        required={required}
        error={error}
        helperText={helperText}
        fullWidth={fullWidth}
        size={size}
        variant="outlined"
        InputProps={{
          readOnly: true,
          endAdornment: (
            <Box display="flex" alignItems="center" gap={0.5}>
              {value && !disabled && (
                <IconButton
                  size="small"
                  onClick={handleClear}
                  sx={{
                    color: "action.active",
                    "&:hover": {
                      color: "action.hover",
                    },
                  }}
                >
                  <ClearIcon fontSize="small" />
                </IconButton>
              )}
              <CalendarIcon sx={{ color: "action.active" }} />
            </Box>
          ),
          sx: {
            cursor: disabled ? "default" : "pointer",
          },
        }}
        sx={{
          "& .MuiInputBase-input": {
            cursor: disabled ? "default" : "pointer",
          },
        }}
      />

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "bottom",
          horizontal: "left",
        }}
        transformOrigin={{
          vertical: "top",
          horizontal: "left",
        }}
        PaperProps={{
          sx: {
            p: 3,
            minWidth: 320,
            maxWidth: 320,
            boxShadow: 3,
          },
        }}
      >
        {loading ? (
          <Box>
            <Skeleton variant="text" width="60%" height={32} />
            <Skeleton variant="rectangular" width="100%" height={200} sx={{ mt: 1 }} />
          </Box>
        ) : apiError ? (
          <Alert severity="error" sx={{ mb: 2 }}>
            {apiError}
          </Alert>
        ) : (
          <Box>
            {/* Header with month navigation */}
            <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
              <IconButton onClick={handlePreviousMonth} size="small" sx={{ color: "text.primary" }}>
                <ChevronLeftIcon fontSize="small" />
              </IconButton>

              <Typography variant="subtitle1" component="div" sx={{ fontWeight: 500, color: "text.primary" }}>
                {format(currentMonth, "MMMM yyyy")}
              </Typography>

              <IconButton onClick={handleNextMonth} size="small" sx={{ color: "text.primary" }}>
                <ChevronRightIcon fontSize="small" />
              </IconButton>
            </Box>

            {/* Week start indicator */}
            <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: "block" }}>
              Week starts on {weekStartsOn}
            </Typography>

            {/* Calendar grid */}
            <Box>
              {/* Day headers with week number column */}
              <Box display="grid" gridTemplateColumns="32px repeat(7, 1fr)" gap={0} mb={1}>
                {/* Week number header */}
                <Box display="flex" alignItems="center" justifyContent="center" py={1}>
                  <Typography variant="caption" color="text.disabled" fontWeight={400} fontSize="0.7rem">
                    W
                  </Typography>
                </Box>

                {(() => {
                  const dayHeaders = weekStartsOn === "Monday" ? ["M", "T", "W", "T", "F", "S", "S"] : ["S", "M", "T", "W", "T", "F", "S"];

                  return dayHeaders.map((day, index) => (
                    <Box key={`${day}-${index}`} display="flex" alignItems="center" justifyContent="center" py={1}>
                      <Typography variant="caption" color="text.secondary" fontWeight={500}>
                        {day}
                      </Typography>
                    </Box>
                  ));
                })()}
              </Box>

              {/* Calendar days with week numbers */}
              <Box>
                {(() => {
                  // Calculate starting position for first day of month
                  const firstDayOfMonth = startOfMonth(currentMonth);
                  const startDayOfWeek = firstDayOfMonth.getDay();
                  const adjustedStartDay = weekStartsOn === "Monday" ? (startDayOfWeek === 0 ? 6 : startDayOfWeek - 1) : startDayOfWeek;

                  // Group days into weeks for proper week number display
                  const allDays: (Date | null)[] = [];

                  // Add empty cells for days before the first day of the month
                  for (let i = 0; i < adjustedStartDay; i++) {
                    allDays.push(null);
                  }

                  // Add actual days
                  allDays.push(...currentMonthDays);

                  // Group into weeks (7 days each)
                  const weeks: (Date | null)[][] = [];
                  for (let i = 0; i < allDays.length; i += 7) {
                    weeks.push(allDays.slice(i, i + 7));
                  }

                  return weeks.map((week, weekIndex) => {
                    // Get the first non-null date in the week to calculate week number
                    const firstDateInWeek = week.find((date) => date !== null);
                    const weekNumber = firstDateInWeek ? getWeekNumber(firstDateInWeek, weekStartsOn) : 1;
                    const isWeekCurrent = firstDateInWeek ? isCurrentWeek(firstDateInWeek, weekStartsOn) : false;

                    return (
                      <Box
                        key={weekIndex}
                        display="grid"
                        gridTemplateColumns="32px repeat(7, 1fr)"
                        gap={0}
                        sx={{
                          backgroundColor: isWeekCurrent ? "rgba(25, 118, 210, 0.04)" : "transparent",
                          borderRadius: 1,
                          transition: "background-color 0.2s ease-in-out",
                        }}
                      >
                        {/* Week number */}
                        <Box display="flex" alignItems="center" justifyContent="center" py={0.5}>
                          <Typography variant="caption" color="text.disabled" fontWeight={400} fontSize="0.7rem">
                            {weekNumber}
                          </Typography>
                        </Box>

                        {/* Days in the week */}
                        {week.map((date, dayIndex) => {
                          if (!date) {
                            return <Box key={`empty-${weekIndex}-${dayIndex}`} height={40} />;
                          }

                          const isSelected = isDateSelected(date);
                          const isHovered = isDateHovered(date);
                          const isTodayDate = isToday(date);

                          return (
                            <Box key={`${weekIndex}-${dayIndex}`} display="flex" alignItems="center" justifyContent="center" py={0.5}>
                              <Box
                                onClick={() => handleDayClick(date)}
                                onMouseEnter={() => handleDayHover(date)}
                                onMouseLeave={() => handleDayHover(null)}
                                sx={{
                                  width: 36,
                                  height: 36,
                                  display: "flex",
                                  alignItems: "center",
                                  justifyContent: "center",
                                  cursor: "pointer",
                                  borderRadius: "50%",
                                  backgroundColor: isSelected ? "primary.main" : isHovered ? "primary.light" : "transparent",
                                  color: isSelected ? "primary.contrastText" : "text.primary",
                                  border: isTodayDate ? "1px solid" : "none",
                                  borderColor: isTodayDate ? "primary.main" : "transparent",
                                  transition: "all 0.2s ease-in-out",
                                  "&:hover": {
                                    backgroundColor: isSelected ? "primary.dark" : "action.hover",
                                  },
                                }}
                              >
                                <Typography variant="body2" fontWeight={isSelected ? 600 : 400}>
                                  {format(date, "d")}
                                </Typography>
                              </Box>
                            </Box>
                          );
                        })}
                      </Box>
                    );
                  });
                })()}
              </Box>
            </Box>

            {/* Footer with current selection */}
            {value && (
              <Box mt={2} pt={2} borderTop="1px solid" borderColor="divider">
                <Typography variant="body2" color="text.secondary">
                  Selected: <strong>{displayValue}</strong>
                </Typography>
              </Box>
            )}
          </Box>
        )}
      </Popover>
    </>
  );
};

export default WeekPicker;
