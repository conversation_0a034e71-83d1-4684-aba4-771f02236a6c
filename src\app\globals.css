@import "tailwindcss";

:root {
  --background: #fafafa;
  --foreground: #424242;
  --font-inter: "Inter", "Roboto", "Helvetica", "Arial", sans-serif;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
}

/* Material Design inspired spacing scale */
:root {
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  --spacing-3xl: 64px;
}

/* Minimalistic color palette */
:root {
  --color-primary: #1976d2;
  --color-primary-light: #42a5f5;
  --color-primary-dark: #1565c0;
  --color-secondary: #757575;
  --color-surface: #ffffff;
  --color-surface-variant: #f5f5f5;
  --color-outline: #e0e0e0;
  --color-text-primary: #424242;
  --color-text-secondary: #757575;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #121212;
    --foreground: #e0e0e0;
    --color-surface: #1e1e1e;
    --color-surface-variant: #2d2d2d;
    --color-outline: #404040;
    --color-text-primary: #e0e0e0;
    --color-text-secondary: #9e9e9e;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Ensure Material UI components work well with Tailwind */
* {
  box-sizing: border-box;
}

/* Custom scrollbar for modern look */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-surface-variant);
}

::-webkit-scrollbar-thumb {
  background: var(--color-outline);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-text-secondary);
}

/* Hidden scrollbars for table containers */
.hidden-scrollbar {
  /* Hide scrollbar for Chrome, Safari and Opera */
  -webkit-scrollbar: none;
  /* Hide scrollbar for IE, Edge and Firefox */
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.hidden-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Enhanced sticky table styling for Google Spreadsheet-like experience */
.sticky-table {
  border-collapse: separate;
  border-spacing: 0;
}

.sticky-table .MuiTableCell-root {
  border-right: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
}

.sticky-table .MuiTableCell-head {
  border-top: 1px solid #e0e0e0;
}

.sticky-table .MuiTableCell-root:first-of-type {
  border-left: 1px solid #e0e0e0;
}

/* Ensure sticky elements have proper layering */
.sticky-header-cell {
  position: sticky !important;
  top: 0 !important;
  z-index: 2 !important;
  background-color: #f5f5f5 !important;
}

.sticky-first-column {
  position: sticky !important;
  left: 0 !important;
  z-index: 1 !important;
  background-color: #ffffff !important;
}

.sticky-corner-cell {
  position: sticky !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 3 !important;
  background-color: #f5f5f5 !important;
}
