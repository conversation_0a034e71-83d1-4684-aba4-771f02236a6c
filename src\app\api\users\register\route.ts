import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/database/prisma";
import { hashPassword } from "@/lib/auth/password";
import { setupDefaultUserData } from "@/lib/utils/defaultData";

interface RegisterData {
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  password: string;
}

export async function POST(request: NextRequest) {
  try {
    console.log("Registration endpoint called");

    const body = (await request.json()) as RegisterData;
    console.log("Request body:", { ...body, password: "[HIDDEN]" });

    // Basic validation
    if (!body.email || !body.username || !body.password || !body.first_name || !body.last_name) {
      return NextResponse.json({ message: "All fields are required" }, { status: 400 });
    }

    // Check if user already exists
    const existingUser = await prisma.auth_user.findFirst({
      where: {
        OR: [{ email: body.email.toLowerCase() }, { username: body.username.toLowerCase() }],
      },
    });

    if (existingUser) {
      return NextResponse.json({ message: "User with this email or username already exists" }, { status: 400 });
    }

    // Hash password
    const hashedPassword = await hashPassword(body.password);

    // Create user
    const newUser = await prisma.auth_user.create({
      data: {
        email: body.email.toLowerCase(),
        username: body.username.toLowerCase(),
        first_name: body.first_name,
        last_name: body.last_name,
        password: hashedPassword,
        is_active: true,
        is_staff: false,
        is_superuser: false,
        date_joined: new Date(),
      },
      select: {
        id: true,
        email: true,
        username: true,
        first_name: true,
        last_name: true,
        is_active: true,
        date_joined: true,
      },
    });

    // Set up default data for the new user (life aspects, preferences, etc.)
    try {
      await setupDefaultUserData(newUser.id);
      console.log("Default data created successfully for user:", newUser.id);
    } catch (defaultDataError) {
      console.error("Error creating default data for user:", newUser.id, defaultDataError);
      // Don't fail registration if default data creation fails
      // The user can still use the app, just without default life aspects
    }

    return NextResponse.json(
      {
        message: "User registered successfully",
        user: newUser,
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Registration error:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
