"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormGroup,
  Checkbox,
  Alert,
  CircularProgress,
  Divider,
  IconButton,
} from "@mui/material";
import { Close as CloseIcon, Tune as TuneIcon } from "@mui/icons-material";
import { userPreferencesService } from "@/lib/api/userPreferencesService";
import { customFieldsService } from "@/lib/api/customFieldsService";
import { UserPreferences, UserPreferencesData } from "@/lib/types/userPreferences";
import { CustomFieldDefinition } from "@/lib/types/customFields";

// Static field definitions for hardcoded project fields
const STATIC_FIELDS = [
  { id: "start_date", name: "Start Date", field_type: "DATE" },
  { id: "end_date", name: "End Date", field_type: "DATE" },
] as const;

interface CustomizeViewModalProps {
  open: boolean;
  onClose: () => void;
  onPreferencesUpdated?: (preferences: UserPreferences) => void;
}

const CustomizeViewModal: React.FC<CustomizeViewModalProps> = ({ open, onClose, onPreferencesUpdated }) => {
  // State management
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [preferences, setPreferences] = useState<UserPreferences | null>(null);
  const [customFields, setCustomFields] = useState<CustomFieldDefinition[]>([]);
  const [allFields, setAllFields] = useState<Array<{ id: string; name: string; field_type: string }>>([]);

  // Form state
  const [pinnedFieldId, setPinnedFieldId] = useState<string | null>(null);
  const [hiddenFieldIds, setHiddenFieldIds] = useState<string[]>([]);

  // Load data when modal opens
  useEffect(() => {
    if (open) {
      loadData();
    }
  }, [open]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load preferences and custom fields in parallel
      const [preferencesData, customFieldsData] = await Promise.all([
        userPreferencesService.getPreferences(),
        customFieldsService.getCustomFieldDefinitions(),
      ]);

      setPreferences(preferencesData);
      const customFieldsArray = Array.isArray(customFieldsData) ? customFieldsData : customFieldsData.results || [];
      setCustomFields(customFieldsArray);

      // Combine static fields with custom fields
      const combinedFields = [
        ...STATIC_FIELDS.map((field) => ({ id: field.id, name: field.name, field_type: field.field_type })),
        ...customFieldsArray.map((field) => ({ id: field.id, name: field.name, field_type: field.field_type })),
      ];
      setAllFields(combinedFields);

      // Initialize form state from preferences
      const projectViewFields = preferencesData?.preferences_data?.project_view_fields;
      setPinnedFieldId(projectViewFields?.pinned_field_id || null);
      setHiddenFieldIds(projectViewFields?.hidden_field_ids || []);
    } catch (err: any) {
      console.error("Error loading customize view data:", err);
      setError(err.message || "Failed to load preferences");
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError(null);

      const updatedPreferencesData: UserPreferencesData = {
        ...preferences?.preferences_data,
        project_view_fields: {
          pinned_field_id: pinnedFieldId,
          hidden_field_ids: hiddenFieldIds,
        },
      };

      const updatedPreferences = await userPreferencesService.updatePreferences(updatedPreferencesData);

      // Notify parent component
      if (onPreferencesUpdated) {
        onPreferencesUpdated(updatedPreferences);
      }

      onClose();
    } catch (err: any) {
      console.error("Error saving preferences:", err);
      setError(err.message || "Failed to save preferences");
    } finally {
      setSaving(false);
    }
  };

  const handlePinnedFieldChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setPinnedFieldId(value === "" ? null : value);
  };

  const handleFieldVisibilityChange = (fieldId: string) => {
    setHiddenFieldIds((prev) => {
      if (prev.includes(fieldId)) {
        // Field is hidden, show it
        return prev.filter((id) => id !== fieldId);
      } else {
        // Field is visible, hide it
        return [...prev, fieldId];
      }
    });
  };

  const isFieldVisible = (fieldId: string) => !hiddenFieldIds.includes(fieldId);
  const isFieldPinned = (fieldId: string) => pinnedFieldId === fieldId;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: "12px",
          boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.15)",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: "space-between",
          pb: 1,
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <TuneIcon color="primary" />
          <Typography variant="h6" component="span">
            Customize Project View
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small" sx={{ color: "text.secondary" }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ pt: 1 }}>
        {loading ? (
          <Box sx={{ display: "flex", justifyContent: "center", py: 4 }}>
            <CircularProgress />
          </Box>
        ) : error ? (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        ) : (
          <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
            {/* Pinned Field Section */}
            <Box>
              <FormControl component="fieldset">
                <FormLabel component="legend" sx={{ mb: 1, fontWeight: 500 }}>
                  Pinned Field
                </FormLabel>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  Choose one field to display prominently next to the project title
                </Typography>

                <RadioGroup value={pinnedFieldId || ""} onChange={handlePinnedFieldChange}>
                  <FormControlLabel value="" control={<Radio size="small" />} label="No pinned field" />
                  {allFields.map((field) => (
                    <FormControlLabel key={field.id} value={field.id} control={<Radio size="small" />} label={field.name} />
                  ))}
                </RadioGroup>
              </FormControl>
            </Box>

            <Divider />

            {/* Field Visibility Section */}
            <Box>
              <FormLabel component="legend" sx={{ mb: 1, fontWeight: 500 }}>
                Field Visibility
              </FormLabel>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                Choose which fields to show or hide in the project view
              </Typography>

              <FormGroup>
                {allFields.map((field) => (
                  <FormControlLabel
                    key={field.id}
                    control={
                      <Checkbox
                        checked={isFieldVisible(field.id)}
                        onChange={() => handleFieldVisibilityChange(field.id)}
                        disabled={isFieldPinned(field.id)} // Pinned fields are always visible
                        size="small"
                      />
                    }
                    label={
                      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                        <Typography variant="body2">{field.name}</Typography>
                        {isFieldPinned(field.id) && (
                          <Typography
                            variant="caption"
                            sx={{
                              color: "primary.main",
                              fontWeight: 500,
                              backgroundColor: "primary.light",
                              px: 1,
                              py: 0.25,
                              borderRadius: 1,
                            }}
                          >
                            Pinned
                          </Typography>
                        )}
                      </Box>
                    }
                  />
                ))}
              </FormGroup>
            </Box>

            {allFields.length === 0 && <Alert severity="info">No fields available for customization.</Alert>}
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 2 }}>
        <Button onClick={onClose} disabled={saving}>
          Cancel
        </Button>
        <Button onClick={handleSave} variant="contained" disabled={loading || saving} sx={{ minWidth: 80 }}>
          {saving ? "Saving..." : "Save"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CustomizeViewModal;
