import { CustomFieldInput } from "@/lib/types/customFields";

export interface ValidationError {
  field: string;
  message: string;
}

export interface OutcomeInput {
  name: string;
  description?: string;
  project_id: string;
  custom_field_inputs?: CustomFieldInput[];
  sort_order?: number;
}

export interface OutcomeUpdate {
  name?: string;
  description?: string;
  project_id?: string;
  custom_field_inputs?: CustomFieldInput[];
  sort_order?: number;
}

export function validateOutcomeName(name: string): string | null {
  if (!name || typeof name !== "string") {
    return "Outcome name is required";
  }

  if (name.trim().length === 0) {
    return "Outcome name cannot be empty";
  }

  if (name.length > 255) {
    return "Outcome name must be less than 255 characters";
  }

  return null;
}

export function validateDescription(description: string): string | null {
  if (description && description.length > 1000) {
    return "Description must be less than 1000 characters";
  }

  return null;
}

export function validateProjectId(projectId: string): string | null {
  if (!projectId || typeof projectId !== "string") {
    return "Project ID is required";
  }

  // Basic ID format validation (supports both UUID and cuid)
  if (projectId.length < 10) {
    return "Invalid project ID format";
  }

  return null;
}

export function validateSortOrder(sortOrder: number): string | null {
  if (sortOrder !== undefined && (typeof sortOrder !== "number" || sortOrder < 0)) {
    return "Sort order must be a non-negative number";
  }

  return null;
}

export function validateCustomFieldInputs(customFieldInputs: CustomFieldInput[]): string[] {
  const errors: string[] = [];

  if (!customFieldInputs || !Array.isArray(customFieldInputs)) {
    return errors; // Optional field
  }

  customFieldInputs.forEach((input, index) => {
    if (!input.definition_id) {
      errors.push(`Custom field input ${index + 1}: definition_id is required`);
    }

    // Basic ID format validation for definition_id (supports both UUID and cuid)
    if (input.definition_id && input.definition_id.length < 10) {
      errors.push(`Custom field input ${index + 1}: invalid definition_id format`);
    }

    // Value can be anything, so we don't validate it here
    // The actual validation will happen against the field definition
  });

  return errors;
}

export function validateCreateOutcome(data: OutcomeInput): ValidationError[] {
  const errors: ValidationError[] = [];

  // Validate name
  const nameError = validateOutcomeName(data.name);
  if (nameError) {
    errors.push({ field: "name", message: nameError });
  }

  // Validate description
  if (data.description) {
    const descriptionError = validateDescription(data.description);
    if (descriptionError) {
      errors.push({ field: "description", message: descriptionError });
    }
  }

  // Validate project_id
  const projectIdError = validateProjectId(data.project_id);
  if (projectIdError) {
    errors.push({ field: "project_id", message: projectIdError });
  }

  // Validate sort_order
  if (data.sort_order !== undefined) {
    const sortOrderError = validateSortOrder(data.sort_order);
    if (sortOrderError) {
      errors.push({ field: "sort_order", message: sortOrderError });
    }
  }

  // Validate custom_field_inputs
  if (data.custom_field_inputs) {
    const customFieldErrors = validateCustomFieldInputs(data.custom_field_inputs);
    customFieldErrors.forEach((error) => {
      errors.push({ field: "custom_field_inputs", message: error });
    });
  }

  return errors;
}

export function validateUpdateOutcome(data: OutcomeUpdate): ValidationError[] {
  const errors: ValidationError[] = [];

  // Validate name if provided
  if (data.name !== undefined) {
    const nameError = validateOutcomeName(data.name);
    if (nameError) {
      errors.push({ field: "name", message: nameError });
    }
  }

  // Validate description if provided
  if (data.description !== undefined) {
    const descriptionError = validateDescription(data.description);
    if (descriptionError) {
      errors.push({ field: "description", message: descriptionError });
    }
  }

  // Validate project_id if provided
  if (data.project_id !== undefined) {
    const projectIdError = validateProjectId(data.project_id);
    if (projectIdError) {
      errors.push({ field: "project_id", message: projectIdError });
    }
  }

  // Validate sort_order if provided
  if (data.sort_order !== undefined) {
    const sortOrderError = validateSortOrder(data.sort_order);
    if (sortOrderError) {
      errors.push({ field: "sort_order", message: sortOrderError });
    }
  }

  // Validate custom_field_inputs if provided
  if (data.custom_field_inputs !== undefined) {
    const customFieldErrors = validateCustomFieldInputs(data.custom_field_inputs);
    customFieldErrors.forEach((error) => {
      errors.push({ field: "custom_field_inputs", message: error });
    });
  }

  return errors;
}
