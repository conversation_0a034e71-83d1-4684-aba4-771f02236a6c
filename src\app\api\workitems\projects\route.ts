import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/database/prisma";
import { verifyToken, extractTokenFromHeader } from "@/lib/auth/jwt";
import { validateCreateProject, ValidationError } from "@/lib/validation/projects";
import { resolveCustomFields, processCustomFieldInputs } from "@/lib/utils/customFieldResolver";
import { applyInheritanceWorkflow } from "@/lib/utils/customFieldInheritance";
import { CreateProjectData } from "@/lib/types/projects";

// Helper function to authenticate request
async function authenticateRequest(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  const token = extractTokenFromHeader(authHeader);

  if (!token) {
    return { error: NextResponse.json({ message: "Authentication required" }, { status: 401 }) };
  }

  const payload = verifyToken(token);
  if (!payload) {
    return { error: NextResponse.json({ message: "Invalid or expired token" }, { status: 401 }) };
  }

  return { userId: payload.userId };
}

// Helper function to create validation error response
function createValidationErrorResponse(errors: ValidationError[]) {
  const errorMap: Record<string, string[]> = {};
  errors.forEach((error) => {
    if (!errorMap[error.field]) errorMap[error.field] = [];
    errorMap[error.field].push(error.message);
  });

  return NextResponse.json(
    {
      message: "Validation failed",
      errors: errorMap,
    },
    { status: 400 }
  );
}

// GET /api/workitems/projects/ - List projects
export async function GET(request: NextRequest) {
  try {
    console.log("GET /api/workitems/projects/ called");

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const lifeAspectId = searchParams.get("life_aspect_id");
    const parentProjectId = searchParams.get("parent_project_id");

    // Build where clause
    const where: any = {
      user_id: auth.userId,
    };

    if (lifeAspectId) {
      where.life_aspect_id = lifeAspectId;
    }

    if (parentProjectId) {
      where.parent_project_id = parentProjectId;
    }

    // Fetch projects with relations
    const projects = await prisma.workitems_project.findMany({
      where,
      include: {
        life_aspect: true,
        parent_project: {
          select: {
            id: true,
            name: true,
          },
        },
        sub_projects: {
          select: {
            id: true,
            name: true,
            sort_order: true,
          },
          orderBy: { sort_order: "asc" },
        },
        outcomes: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: [{ sort_order: "asc" }, { created_at: "asc" }],
    });

    // Resolve custom fields for each project
    const projectsWithCustomFields = await Promise.all(
      projects.map(async (project) => {
        const resolvedCustomFields = await resolveCustomFields(project.custom_field_values as Record<string, any>, "PROJECT", auth.userId);

        return {
          ...project,
          resolved_custom_fields: resolvedCustomFields,
        };
      })
    );

    console.log(`Found ${projectsWithCustomFields.length} projects for user ${auth.userId}`);

    return NextResponse.json(projectsWithCustomFields);
  } catch (error) {
    console.error("Error fetching projects:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// POST /api/workitems/projects/ - Create project
export async function POST(request: NextRequest) {
  try {
    console.log("POST /api/workitems/projects/ called");

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Parse request body
    const body = (await request.json()) as CreateProjectData;
    console.log("Request body:", { ...body, custom_field_inputs: body.custom_field_inputs?.length || 0 });

    // Validate input
    const validationErrors = validateCreateProject(body);
    if (validationErrors.length > 0) {
      return createValidationErrorResponse(validationErrors);
    }

    // Verify life aspect exists and belongs to user
    const lifeAspect = await prisma.workitems_life_aspect.findFirst({
      where: {
        id: body.life_aspect,
        user_id: auth.userId,
      },
    });

    if (!lifeAspect) {
      return NextResponse.json(
        {
          message: "Life aspect not found or does not belong to user",
        },
        { status: 400 }
      );
    }

    // Verify parent project exists and belongs to user (if specified)
    if (body.parent_project) {
      const parentProject = await prisma.workitems_project.findFirst({
        where: {
          id: body.parent_project,
          user_id: auth.userId,
        },
      });

      if (!parentProject) {
        return NextResponse.json(
          {
            message: "Parent project not found or does not belong to user",
          },
          { status: 400 }
        );
      }
    }

    // Process custom field inputs with inheritance
    let customFieldValues = {};
    try {
      customFieldValues = await applyInheritanceWorkflow(body.parent_project || null, auth.userId, body.custom_field_inputs || [], "PROJECT");
    } catch (error) {
      return NextResponse.json(
        {
          message: "Custom field processing failed",
          error: error instanceof Error ? error.message : "Unknown error",
        },
        { status: 400 }
      );
    }

    // Create project
    const project = await prisma.workitems_project.create({
      data: {
        user_id: auth.userId,
        name: body.name,
        description: body.description || null,
        life_aspect_id: body.life_aspect,
        parent_project_id: body.parent_project || null,
        start_date: body.start_date ? new Date(body.start_date) : null,
        end_date: body.end_date ? new Date(body.end_date) : null,
        custom_field_values: customFieldValues,
        sort_order: body.sort_order || 0,
      },
      include: {
        life_aspect: true,
        parent_project: {
          select: {
            id: true,
            name: true,
          },
        },
        sub_projects: {
          select: {
            id: true,
            name: true,
            sort_order: true,
          },
          orderBy: { sort_order: "asc" },
        },
        outcomes: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Resolve custom fields
    const resolvedCustomFields = await resolveCustomFields(project.custom_field_values as Record<string, any>, "PROJECT", auth.userId);

    const projectWithCustomFields = {
      ...project,
      resolved_custom_fields: resolvedCustomFields,
    };

    console.log("Created project:", project.id);

    return NextResponse.json(projectWithCustomFields, { status: 201 });
  } catch (error) {
    console.error("Error creating project:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
