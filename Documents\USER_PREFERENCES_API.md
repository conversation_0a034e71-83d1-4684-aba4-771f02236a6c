# User Preferences API Documentation

## Overview

The User Preferences API provides endpoints for managing user-specific display preferences and UI customizations. This API is built with Next.js API routes and Prisma ORM, supporting the frontend's "Customize View" functionality, allowing users to personalize their experience with custom field display settings.

## Endpoint

**Base URL:** `/api/users/me/preferences`

## Authentication

All endpoints require authentication using JWT Bearer tokens.

**Headers:**

```
Authorization: Bearer <your-jwt-token>
```

## Supported HTTP Methods

### GET - Retrieve User Preferences

Retrieves the current user's preferences. If no preferences exist, creates and returns default empty preferences.

**Request:**

```http
GET /api/users/me/preferences
Authorization: Bearer <your-jwt-token>
```

**Response (200 OK):**

```json
{
  "preferences_data": {
    "project_view_fields": {
      "pinned_field_id": "uuid-of-priority-custom-field",
      "hidden_field_ids": ["uuid-of-status-custom-field"]
    }
  },
  "week_starts_on": "Sunday",
  "created_at": "2025-06-11T09:17:17.693917Z",
  "updated_at": "2025-06-11T09:17:17.870592Z"
}
```

### PUT - Update User Preferences (Full Replace)

Completely replaces the user's preferences with the provided data.

**Request:**

```http
PUT /api/users/me/preferences
Authorization: Bearer <your-jwt-token>
Content-Type: application/json

{
  "preferences_data": {
    "project_view_fields": {
      "pinned_field_id": "047fbe18-b2d3-40e9-b665-e653606819cc",
      "hidden_field_ids": ["123e4567-e89b-12d3-a456-************"]
    }
  },
  "week_starts_on": "Monday"
}
```

**Response (200 OK):**

```json
{
  "preferences_data": {
    "project_view_fields": {
      "pinned_field_id": "047fbe18-b2d3-40e9-b665-e653606819cc",
      "hidden_field_ids": ["123e4567-e89b-12d3-a456-************"]
    }
  },
  "week_starts_on": "Monday",
  "created_at": "2025-06-11T09:17:17.693917Z",
  "updated_at": "2025-06-11T09:17:17.870592Z"
}
```

### PATCH - Partial Update User Preferences

Partially updates the user's preferences, merging with existing data.

**Request:**

```http
PATCH /api/users/me/preferences
Authorization: Bearer <your-jwt-token>
Content-Type: application/json

{
  "preferences_data": {
    "project_view_fields": {
      "pinned_field_id": "047fbe18-b2d3-40e9-b665-e653606819cc"
    }
  }
}
```

## Data Structure

### preferences_data

The `preferences_data` field is a JSON object that can contain various user preference categories:

#### project_view_fields

Configuration for custom field display in the Project Overview page.

**Structure:**

```json
{
  "project_view_fields": {
    "pinned_field_id": "string|null",
    "hidden_field_ids": ["string"]
  }
}
```

**Fields:**

- `pinned_field_id` (string|null): UUID of the custom field to pin next to the project title. Set to `null` to unpin.
- `hidden_field_ids` (array): Array of custom field UUIDs to hide from the view.

#### week_starts_on

User preference for the start day of the week, used in weekly planning and calendar views.

**Type:** `string`

**Valid Values:** `"Sunday"` or `"Monday"`

**Default:** `"Sunday"`

## Validation

### Custom Field ID Validation

- All custom field IDs must be valid UUIDs
- Custom field IDs must reference existing CustomFieldDefinition records
- Custom field IDs must belong to the authenticated user
- Custom field IDs must target the PROJECT model for project_view_fields

### Data Structure Validation

- `preferences_data` must be a valid JSON object
- `project_view_fields` must be a JSON object if present
- `hidden_field_ids` must be an array if present
- Each item in `hidden_field_ids` must be a string (UUID)

## Error Responses

### 400 Bad Request - Validation Error

```json
{
  "preferences_data": {
    "project_view_fields": {
      "pinned_field_id": "Invalid custom field ID or field does not belong to user."
    }
  }
}
```

### 401 Unauthorized

```json
{
  "detail": "Authentication credentials were not provided."
}
```

### 403 Forbidden

```json
{
  "detail": "You do not have permission to perform this action."
}
```

## Usage Examples

### Frontend Integration

```javascript
// Get user preferences
const getPreferences = async () => {
  const response = await fetch("/api/users/me/preferences", {
    headers: {
      Authorization: `Bearer ${userJwtToken}`,
      "Content-Type": "application/json",
    },
  });
  return response.json();
};

// Update preferences
const updatePreferences = async (preferences) => {
  const response = await fetch("/api/users/me/preferences", {
    method: "PUT",
    headers: {
      Authorization: `Bearer ${userJwtToken}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      preferences_data: preferences,
    }),
  });
  return response.json();
};

// Pin a custom field
const pinCustomField = async (fieldId) => {
  const preferences = await getPreferences();
  const updatedPreferences = {
    ...preferences.preferences_data,
    project_view_fields: {
      ...preferences.preferences_data.project_view_fields,
      pinned_field_id: fieldId,
    },
  };
  return updatePreferences(updatedPreferences);
};

// Hide custom fields
const hideCustomFields = async (fieldIds) => {
  const preferences = await getPreferences();
  const updatedPreferences = {
    ...preferences.preferences_data,
    project_view_fields: {
      ...preferences.preferences_data.project_view_fields,
      hidden_field_ids: fieldIds,
    },
  };
  return updatePreferences(updatedPreferences);
};
```

## Database Schema

The preferences are stored in the `auth_user_preferences` table using Prisma ORM:

```prisma
model auth_user_preferences {
  id             String   @id @default(cuid())
  user_id        String   @unique
  preferences    Json     @default("{}")
  week_starts_on String   @default("Sunday") @db.VarChar(10)
  created_at     DateTime @default(now())
  updated_at     DateTime @updatedAt

  // Relations
  user auth_user @relation(fields: [user_id], references: [id], onDelete: Cascade)

  @@map("auth_user_preferences")
}
```

## Notes

- Each user can have only one UserPreferences record (one-to-one relationship)
- Preferences are automatically created when first accessed via GET
- The API supports extensibility - new preference categories can be added to `preferences_data` without schema changes
- All timestamps are in UTC
- The API follows RESTful conventions with Next.js API routes and Prisma ORM
- JWT authentication is required for all endpoints
