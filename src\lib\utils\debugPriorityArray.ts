/**
 * Debug utilities for Priority Dropdown array formatting issue
 * Temporary file for testing custom_field_inputs array formatting
 */

import { createPriorityUpdateInput, priorityUpdateToCustomFieldInputs } from "./customFieldExtractors";

/**
 * Test function to verify custom field inputs array formatting
 * Call this from browser console to test the utility functions
 */
export function testPriorityArrayFormatting(definitionId: string, choiceId: string) {
  console.log("=== PRIORITY ARRAY FORMATTING TEST ===");
  console.log("Input - Definition ID:", definitionId);
  console.log("Input - Choice ID:", choiceId);
  
  try {
    // Step 1: Create priority update input
    const priorityUpdate = createPriorityUpdateInput(definitionId, choiceId);
    console.log("Step 1 - Priority Update Input:", priorityUpdate);
    console.log("Step 1 - Type:", typeof priorityUpdate);
    console.log("Step 1 - Keys:", Object.keys(priorityUpdate));
    
    // Step 2: Convert to custom field inputs
    const customFieldInputs = priorityUpdateToCustomFieldInputs(priorityUpdate);
    console.log("Step 2 - Custom Field Inputs:", customFieldInputs);
    console.log("Step 2 - Type:", typeof customFieldInputs);
    console.log("Step 2 - Is Array:", Array.isArray(customFieldInputs));
    console.log("Step 2 - Length:", customFieldInputs?.length);
    
    // Step 3: Create API payload
    const apiPayload = {
      custom_field_inputs: customFieldInputs,
    };
    console.log("Step 3 - API Payload:", apiPayload);
    console.log("Step 3 - Payload Type:", typeof apiPayload);
    console.log("Step 3 - custom_field_inputs Type:", typeof apiPayload.custom_field_inputs);
    console.log("Step 3 - custom_field_inputs Is Array:", Array.isArray(apiPayload.custom_field_inputs));
    
    // Step 4: JSON serialization test
    const jsonString = JSON.stringify(apiPayload);
    console.log("Step 4 - JSON String:", jsonString);
    
    const parsedBack = JSON.parse(jsonString);
    console.log("Step 4 - Parsed Back:", parsedBack);
    console.log("Step 4 - Parsed custom_field_inputs Is Array:", Array.isArray(parsedBack.custom_field_inputs));
    
    // Step 5: Validation
    const isValid = validateCustomFieldInputsArray(apiPayload.custom_field_inputs);
    console.log("Step 5 - Validation Result:", isValid);
    
    console.log("=== TEST COMPLETED SUCCESSFULLY ===");
    return {
      success: true,
      priorityUpdate,
      customFieldInputs,
      apiPayload,
      jsonString,
      parsedBack,
      isValid,
    };
    
  } catch (error) {
    console.error("=== TEST FAILED ===");
    console.error("Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

/**
 * Validate custom field inputs array format
 */
export function validateCustomFieldInputsArray(customFieldInputs: any): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  
  // Check if it's an array
  if (!Array.isArray(customFieldInputs)) {
    errors.push("custom_field_inputs is not an array");
    return { isValid: false, errors };
  }
  
  // Check if array is not empty
  if (customFieldInputs.length === 0) {
    errors.push("custom_field_inputs array is empty");
    return { isValid: false, errors };
  }
  
  // Validate each item in the array
  customFieldInputs.forEach((item, index) => {
    if (!item || typeof item !== 'object') {
      errors.push(`Item ${index} is not an object`);
      return;
    }
    
    if (!item.definition_id) {
      errors.push(`Item ${index} missing definition_id`);
    }
    
    if (item.value === undefined) {
      errors.push(`Item ${index} missing value field`);
    }
    
    if (typeof item.definition_id !== 'string') {
      errors.push(`Item ${index} definition_id is not a string`);
    }
  });
  
  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Test with sample data
 */
export function runSampleTest() {
  console.log("=== RUNNING SAMPLE TEST ===");
  
  const sampleDefinitionId = "sample-definition-uuid";
  const sampleChoiceId = "sample-choice-uuid";
  
  return testPriorityArrayFormatting(sampleDefinitionId, sampleChoiceId);
}

/**
 * Test clear priority payload formatting
 */
export function testClearPriorityFormatting(definitionId: string) {
  console.log("=== CLEAR PRIORITY FORMATTING TEST ===");
  console.log("Input - Definition ID:", definitionId);
  
  try {
    const clearPayload = {
      custom_field_inputs: [
        {
          definition_id: definitionId,
          value: null,
        },
      ],
    };
    
    console.log("Clear Payload:", clearPayload);
    console.log("Type:", typeof clearPayload);
    console.log("custom_field_inputs Type:", typeof clearPayload.custom_field_inputs);
    console.log("custom_field_inputs Is Array:", Array.isArray(clearPayload.custom_field_inputs));
    console.log("custom_field_inputs Length:", clearPayload.custom_field_inputs?.length);
    
    const jsonString = JSON.stringify(clearPayload);
    console.log("JSON String:", jsonString);
    
    const parsedBack = JSON.parse(jsonString);
    console.log("Parsed Back:", parsedBack);
    console.log("Parsed custom_field_inputs Is Array:", Array.isArray(parsedBack.custom_field_inputs));
    
    const isValid = validateCustomFieldInputsArray(clearPayload.custom_field_inputs);
    console.log("Validation Result:", isValid);
    
    console.log("=== CLEAR TEST COMPLETED ===");
    return {
      success: true,
      clearPayload,
      jsonString,
      parsedBack,
      isValid,
    };
    
  } catch (error) {
    console.error("=== CLEAR TEST FAILED ===");
    console.error("Error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

// Make functions available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testPriorityArrayFormatting = testPriorityArrayFormatting;
  (window as any).validateCustomFieldInputsArray = validateCustomFieldInputsArray;
  (window as any).runSampleTest = runSampleTest;
  (window as any).testClearPriorityFormatting = testClearPriorityFormatting;
}
