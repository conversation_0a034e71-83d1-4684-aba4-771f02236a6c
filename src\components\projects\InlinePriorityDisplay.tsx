"use client";

import React from "react";
import { Box, Typography } from "@mui/material";
import { getContrastingTextColor } from "@/lib/utils/colorUtils";

interface InlinePriorityDisplayProps {
  priorityId?: string;
  priorityName?: string;
  priorityColor?: string;
  onClick: (event: React.MouseEvent<HTMLElement>) => void;
  isEditing?: boolean;
}

const InlinePriorityDisplay: React.FC<InlinePriorityDisplayProps> = ({
  priorityId,
  priorityName,
  priorityColor,
  onClick,
  isEditing = false,
}) => {
  // If no priority is set, show placeholder
  if (!priorityId || !priorityName) {
    return (
      <Typography
        component="span"
        onClick={onClick}
        sx={{
          color: "text.secondary",
          fontSize: "0.875rem",
          fontStyle: "italic",
          cursor: "pointer",
          padding: "2px 6px",
          borderRadius: "4px",
          "&:hover": {
            backgroundColor: "action.hover",
          },
          opacity: isEditing ? 0.5 : 1,
          transition: "all 0.2s ease-in-out",
        }}
      >
        [Set Priority]
      </Typography>
    );
  }

  // Determine text color for accessibility
  const textColor = priorityColor ? getContrastingTextColor(priorityColor) : "#ffffff";

  // Show priority as a badge
  return (
    <Box
      component="span"
      onClick={onClick}
      sx={{
        display: "inline-flex",
        alignItems: "center",
        backgroundColor: priorityColor || "#757575",
        color: textColor,
        fontSize: "0.75rem",
        fontWeight: 500,
        padding: "2px 8px",
        borderRadius: "12px", // Pill-shaped badge
        cursor: "pointer",
        opacity: isEditing ? 0.5 : 1,
        transition: "all 0.2s ease-in-out",
        "&:hover": {
          transform: "scale(1.05)",
          boxShadow: "0px 2px 4px rgba(0, 0, 0, 0.15)",
        },
        // Ensure minimum contrast for readability
        minHeight: "20px",
        lineHeight: 1.2,
        whiteSpace: "nowrap",
      }}
    >
      {priorityName}
    </Box>
  );
};

export default InlinePriorityDisplay;
