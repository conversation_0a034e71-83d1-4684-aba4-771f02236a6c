# Agile Life Results System - Frontend

A modern Next.js full-stack application for the Agile Life Results System, designed to help users achieve a balanced and fulfilling life through structured goal management.

## 🚀 Features

- **Modern Tech Stack**: Built with Next.js 15.3.2, TypeScript, Material-UI v7, and Tailwind CSS v4
- **Full-Stack Architecture**: Complete Next.js API routes with Prisma ORM and PostgreSQL
- **Authentication System**: JWT-based authentication with bcrypt password hashing
- **Responsive Design**: Material-UI components with custom theme and Tailwind CSS utilities
- **Type Safety**: Full TypeScript support with Prisma-generated types
- **Component Architecture**: Well-organized component structure with Material-UI design system

## 📁 Project Structure

```
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/                # API routes (Next.js backend)
│   │   │   ├── users/          # User authentication endpoints
│   │   │   └── workitems/      # Project, outcome, custom fields APIs
│   │   ├── globals.css         # Global styles (Tailwind + Material-UI)
│   │   ├── layout.tsx          # Root layout with providers
│   │   └── page.tsx            # Home page
│   ├── components/             # Reusable UI components
│   │   ├── customFields/       # Custom fields management
│   │   ├── layout/             # Layout components
│   │   └── projects/           # Project-related components
│   ├── lib/                    # Core utilities and services
│   │   ├── api/                # API service layer
│   │   ├── auth/               # JWT authentication logic
│   │   ├── database/           # Prisma client configuration
│   │   ├── theme/              # Material-UI theme
│   │   ├── types/              # TypeScript type definitions
│   │   └── utils/              # Utility functions
│   └── generated/              # Prisma generated client
├── prisma/
│   └── schema.prisma           # Database schema
└── .env.local                  # Environment variables
```

## 🛠️ Setup Instructions

### Prerequisites

- Node.js 18+
- PostgreSQL database
- npm or yarn

### Installation

1. **Clone and navigate to the project:**

   ```bash
   cd minimisia_frontend_only
   ```

2. **Install dependencies:**

   ```bash
   npm install
   ```

3. **Environment Configuration:**
   Create a `.env.local` file with:

   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/minimisia"
   JWT_SECRET="your-jwt-secret-key"
   NEXTAUTH_SECRET="your-nextauth-secret"
   ```

4. **Set up the database:**

   ```bash
   npx prisma generate
   npx prisma db push
   ```

5. **Start the development server:**

   ```bash
   npm run dev
   ```

6. **Open your browser:**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🔧 Configuration

### API Configuration

The application uses Next.js API routes for backend functionality:

- **Base URL**: `/api` (internal Next.js routes)
- **Authentication**: JWT tokens with bcrypt password hashing
- **Database**: PostgreSQL with Prisma ORM
- **Content Type**: `application/json`

### Authentication Flow

1. **Login/Register**: Users authenticate via Next.js API routes (`/api/users/login`, `/api/users/register`)
2. **Token Storage**: JWT tokens stored in localStorage
3. **Auto-login**: Persistent sessions across browser refreshes
4. **Global State**: Authentication state managed via React Context
5. **Password Security**: bcrypt hashing for secure password storage

## 📚 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema changes to database
- `npm run db:studio` - Open Prisma Studio

## 🎨 Styling

- **UI Framework**: Material-UI v7 with custom theme system
- **Utility CSS**: Tailwind CSS v4 with `@import "tailwindcss"` syntax
- **Fonts**: Inter font family for clean, modern typography
- **Theme**: Material Design 3 inspired minimalistic theme
- **Components**: Material-UI components with custom styling overrides

## 🔗 API Integration

### Authentication Endpoints

The application provides these Next.js API routes:

- `POST /api/users/login` - User login
- `POST /api/users/register` - User registration
- `GET /api/users/me` - Get user profile

### Workitems Endpoints

- `GET /api/workitems/projects` - List projects
- `POST /api/workitems/projects` - Create project
- `GET /api/workitems/custom-fields` - List custom field definitions
- `POST /api/workitems/custom-fields` - Create custom field definition

### Example API Response Format

```typescript
// Login/Register Response
{
  "token": "your-jwt-token",
  "user": {
    "id": "cuid-string",
    "username": "john_doe",
    "email": "<EMAIL>",
    "first_name": "John",
    "last_name": "Doe",
    "date_joined": "2024-01-01T00:00:00Z",
    "is_active": true
  }
}
```

## 🚀 Current Implementation Status

This is a fully functional application with:

1. ✅ **Authentication System**: Complete JWT-based auth with login/register
2. ✅ **Project Management**: Hierarchical project structure with Life Aspects
3. ✅ **Custom Fields System**: Dynamic field definitions with multiple types
4. ✅ **User Interface**: Material-UI v7 components with responsive design
5. ✅ **Database Integration**: Prisma ORM with PostgreSQL
6. ✅ **Type Safety**: Full TypeScript implementation
7. ✅ **API Layer**: Complete Next.js API routes

## 📚 Additional Documentation

For comprehensive documentation, see the `documents/` folder:

- **[DOCUMENTATION_INDEX.md](documents/DOCUMENTATION_INDEX.md)** - Complete documentation overview
- **[MIGRATION_GUIDE.md](documents/MIGRATION_GUIDE.md)** - Django to Next.js migration guide
- **[STYLE_GUIDE.md](documents/STYLE_GUIDE.md)** - Material-UI v7 design system
- **[USING_CUSTOM_APIS.md](documents/USING_CUSTOM_APIS.md)** - API usage guide
- **[CUSTOM_FIELDS_IMPLEMENTATION_SUMMARY.md](documents/CUSTOM_FIELDS_IMPLEMENTATION_SUMMARY.md)** - System architecture
- **[USER_PREFERENCES_API.md](documents/USER_PREFERENCES_API.md)** - User preferences API

## 🤝 Contributing

1. Follow the established project structure
2. Use TypeScript for all new files
3. Follow the existing naming conventions
4. Test your changes thoroughly

## 📄 License

This project is part of the Agile Life Results System.
