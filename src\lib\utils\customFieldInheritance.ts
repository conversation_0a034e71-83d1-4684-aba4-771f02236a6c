/**
 * Custom Field Inheritance Utility
 * Handles automatic inheritance of custom field values from parent projects to child items
 */

import { prisma } from "@/lib/database/prisma";
import { CustomFieldInput } from "@/lib/types/customFields";

/**
 * Apply custom field inheritance for new work items
 *
 * @param parentId - ID of the parent project to inherit from
 * @param userId - ID of the user creating the item
 * @param userProvidedInputs - Custom field inputs explicitly provided by the user
 * @returns Promise<Record<string, any>> - The custom field values to apply to the new item
 */
export async function applyCustomFieldInheritance(
  parentId: string,
  userId: string,
  userProvidedInputs: CustomFieldInput[] = []
): Promise<Record<string, any>> {
  try {
    console.log("🔄 Applying custom field inheritance from parent:", parentId);

    // Step 1: Fetch parent's custom field values
    const parentProject = await prisma.workitems_project.findFirst({
      where: {
        id: parentId,
        user_id: userId,
      },
      select: {
        id: true,
        name: true,
        custom_field_values: true,
      },
    });

    if (!parentProject) {
      console.log("❌ Parent project not found, skipping inheritance");
      return {};
    }

    console.log("✅ Found parent project:", parentProject.name);
    const parentCustomFieldValues = (parentProject.custom_field_values as Record<string, any>) || {};
    console.log("📊 Parent custom field values:", Object.keys(parentCustomFieldValues).length, "fields");

    // Step 2: Fetch all available custom field definitions for the user
    const customFieldDefinitions = await prisma.workitems_custom_field_definition.findMany({
      where: {
        user_id: userId,
      },
      include: {
        choice_options: {
          orderBy: { sort_order: "asc" },
        },
      },
      orderBy: { sort_order: "asc" },
    });

    console.log("📋 Found", customFieldDefinitions.length, "custom field definitions");

    // Step 3: Create a map of user-provided inputs for quick lookup
    const userInputsMap = new Map<string, any>();
    userProvidedInputs.forEach((input) => {
      userInputsMap.set(input.definition_id, input.value);
    });

    console.log("👤 User provided values for", userInputsMap.size, "fields");

    // Step 4: Apply inheritance rules for each custom field definition
    const inheritedValues: Record<string, any> = {};

    for (const definition of customFieldDefinitions) {
      const definitionId = definition.id;

      // Skip if user has explicitly provided a value for this field
      if (userInputsMap.has(definitionId)) {
        console.log(`⏭️  Skipping field "${definition.name}" - user provided value`);
        continue;
      }

      // Rule 1: Direct Inheritance - Check if parent has a value for this field
      if (parentCustomFieldValues[definitionId] !== undefined && parentCustomFieldValues[definitionId] !== null) {
        inheritedValues[definitionId] = parentCustomFieldValues[definitionId];
        console.log(`📥 Inherited "${definition.name}" from parent:`, parentCustomFieldValues[definitionId]);
        continue;
      }

      // Rule 2: Field's Default Value - Check if field has a default choice option
      if (definition.field_type === "SINGLE_SELECT") {
        const defaultOption = definition.choice_options.find((option) => option.is_default === true);
        if (defaultOption) {
          inheritedValues[definitionId] = defaultOption.id;
          console.log(`🌟 Applied default value for "${definition.name}":`, defaultOption.value);
          continue;
        }
      }

      // Rule 3: No Value - Field is left empty
      console.log(`⭕ No value set for "${definition.name}" - leaving empty`);
    }

    console.log("✅ Inheritance complete. Applied values for", Object.keys(inheritedValues).length, "fields");
    return inheritedValues;
  } catch (error) {
    console.error("❌ Error applying custom field inheritance:", error);
    throw error;
  }
}

/**
 * Merge user-provided custom field inputs with inherited values
 * User-provided values take precedence over inherited values
 *
 * @param userProvidedInputs - Custom field inputs explicitly provided by the user
 * @param inheritedValues - Values inherited from parent or defaults
 * @param userId - ID of the user (for validation)
 * @returns Promise<Record<string, any>> - The final custom field values to store
 */
export async function mergeCustomFieldValues(
  userProvidedInputs: CustomFieldInput[],
  inheritedValues: Record<string, any>,
  userId: string
): Promise<Record<string, any>> {
  try {
    console.log("🔀 Merging user inputs with inherited values");

    // Start with inherited values as the base
    const finalValues = { ...inheritedValues };

    // Process user-provided inputs (these take precedence)
    if (userProvidedInputs && userProvidedInputs.length > 0) {
      // Import the processCustomFieldInputs function
      const { processCustomFieldInputs } = await import("./customFieldResolver");

      const userValues = await processCustomFieldInputs(
        userProvidedInputs,
        "PROJECT", // This will be overridden by the caller for outcomes
        userId,
        finalValues
      );

      // Merge user values over inherited values
      Object.assign(finalValues, userValues);
      console.log("✅ Merged", userProvidedInputs.length, "user-provided values");
    }

    console.log("📊 Final custom field values:", Object.keys(finalValues).length, "fields");
    return finalValues;
  } catch (error) {
    console.error("❌ Error merging custom field values:", error);
    throw error;
  }
}

/**
 * Complete inheritance workflow for new work items
 * This is the main function to call when creating new projects or outcomes
 *
 * @param parentId - ID of the parent project (null for root projects)
 * @param userId - ID of the user creating the item
 * @param userProvidedInputs - Custom field inputs explicitly provided by the user
 * @param targetModel - Type of work item being created
 * @returns Promise<Record<string, any>> - The custom field values to apply
 */
export async function applyInheritanceWorkflow(
  parentId: string | null,
  userId: string,
  userProvidedInputs: CustomFieldInput[] = [],
  targetModel: "PROJECT" | "OUTCOME" = "PROJECT"
): Promise<Record<string, any>> {
  try {
    console.log("🚀 Starting inheritance workflow for", targetModel);

    // Check user's inheritance preference first
    const userPreferences = await prisma.auth_user_preferences.findUnique({
      where: { user_id: userId },
      select: { enable_inheritance: true },
    });

    const inheritanceEnabled = userPreferences?.enable_inheritance ?? true; // Default to true if no preference found
    console.log("🔧 Inheritance enabled:", inheritanceEnabled);

    // If inheritance is disabled, only process user inputs and defaults (no parent inheritance)
    if (!inheritanceEnabled || !parentId) {
      console.log("📝 Inheritance disabled or no parent specified, applying defaults only");

      if (userProvidedInputs.length === 0) {
        // Apply field defaults for fields with default values
        return await applyFieldDefaults(userId);
      } else {
        // Process user inputs and merge with defaults
        const { processCustomFieldInputs } = await import("./customFieldResolver");
        const userValues = await processCustomFieldInputs(userProvidedInputs, targetModel, userId);
        const defaultValues = await applyFieldDefaults(userId);
        return { ...defaultValues, ...userValues };
      }
    }

    // Apply inheritance from parent (only if inheritance is enabled)
    const inheritedValues = await applyCustomFieldInheritance(parentId, userId, userProvidedInputs);

    // Merge with user-provided inputs
    const finalValues = await mergeCustomFieldValues(userProvidedInputs, inheritedValues, userId);

    return finalValues;
  } catch (error) {
    console.error("❌ Error in inheritance workflow:", error);
    throw error;
  }
}

/**
 * Apply default values for fields that have default choice options
 * Used when creating root items with no parent
 */
async function applyFieldDefaults(userId: string): Promise<Record<string, any>> {
  const defaultValues: Record<string, any> = {};

  // Get all SINGLE_SELECT fields with default values
  const fieldsWithDefaults = await prisma.workitems_custom_field_definition.findMany({
    where: {
      user_id: userId,
      field_type: "SINGLE_SELECT",
    },
    include: {
      choice_options: {
        where: { is_default: true },
      },
    },
  });

  for (const field of fieldsWithDefaults) {
    if (field.choice_options.length > 0) {
      const defaultOption = field.choice_options[0];
      defaultValues[field.id] = defaultOption.id;
      console.log(`🌟 Applied default value for "${field.name}":`, defaultOption.value);
    }
  }

  return defaultValues;
}
