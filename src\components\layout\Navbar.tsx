"use client";

import { useAuth } from "@/lib/auth/AuthContext";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { AppBar, Toolbar, Typography, Button, Box, CircularProgress, Stack, Avatar, Menu, MenuItem, Divider, IconButton } from "@mui/material";
import { Login, PersonAdd, Logout, AccountCircle, Settings, Dashboard, Category, ViewModule } from "@mui/icons-material";
import { useState } from "react";

const Navbar = () => {
  const { isAuthenticated, user, logout, isLoading } = useAuth();
  const router = useRouter();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);

  const handleUserMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = async () => {
    try {
      await logout();
      handleUserMenuClose();
      router.push("/");
    } catch (error) {
      console.error("Logout error:", error);
    }
  };

  const handleNavigation = (path: string) => {
    handleUserMenuClose();
    router.push(path);
  };

  return (
    <AppBar
      position="static"
      elevation={1}
      sx={{
        bgcolor: "background.paper",
        color: "text.primary",
        borderBottom: 1,
        borderColor: "divider",
      }}
    >
      <Toolbar sx={{ px: { xs: 2, sm: 3 }, minHeight: { xs: 48, sm: 56 }, py: 0.5 }}>
        <Typography
          variant="h6"
          component="div"
          sx={{
            flexGrow: 1,
            fontWeight: 600,
            color: "primary.main",
          }}
        >
          Agile Life Results
        </Typography>

        <Box>
          {isLoading ? (
            <CircularProgress size={24} />
          ) : isAuthenticated && user ? (
            <Stack direction="row" spacing={2} alignItems="center">
              <Typography variant="body2" color="text.secondary" sx={{ display: { xs: "none", sm: "block" } }}>
                Welcome, {user.first_name || user.username}
              </Typography>
              <IconButton
                onClick={handleUserMenuOpen}
                size="small"
                sx={{
                  color: "text.secondary",
                  "&:hover": {
                    color: "primary.main",
                  },
                }}
              >
                <Avatar sx={{ width: 32, height: 32, bgcolor: "primary.main" }}>{(user.first_name?.[0] || user.username[0]).toUpperCase()}</Avatar>
              </IconButton>
              <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleUserMenuClose}
                onClick={handleUserMenuClose}
                transformOrigin={{ horizontal: "right", vertical: "top" }}
                anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
              >
                <MenuItem onClick={() => handleNavigation("/dashboard")}>
                  <Dashboard sx={{ mr: 1 }} />
                  Dashboard
                </MenuItem>
                <MenuItem onClick={() => handleNavigation("/overview")}>
                  <ViewModule sx={{ mr: 1 }} />
                  Project Overview
                </MenuItem>
                <MenuItem onClick={() => handleNavigation("/dashboard/life-aspects")}>
                  <Category sx={{ mr: 1 }} />
                  Life Aspects
                </MenuItem>
                <MenuItem onClick={() => handleNavigation("/profile")}>
                  <AccountCircle sx={{ mr: 1 }} />
                  Profile
                </MenuItem>
                <MenuItem onClick={() => handleNavigation("/settings")}>
                  <Settings sx={{ mr: 1 }} />
                  Settings
                </MenuItem>
                <Divider />
                <MenuItem onClick={handleLogout}>
                  <Logout sx={{ mr: 1 }} />
                  Logout
                </MenuItem>
              </Menu>
            </Stack>
          ) : (
            <Stack direction="row" spacing={1}>
              <Button
                component={Link}
                href="/login"
                variant="text"
                size="small"
                startIcon={<Login />}
                sx={{
                  borderRadius: 2,
                  textTransform: "none",
                }}
              >
                Login
              </Button>
              <Button
                component={Link}
                href="/register"
                variant="contained"
                size="small"
                startIcon={<PersonAdd />}
                sx={{
                  borderRadius: 2,
                  textTransform: "none",
                }}
              >
                Register
              </Button>
            </Stack>
          )}
        </Box>
      </Toolbar>
    </AppBar>
  );
};

export default Navbar;
