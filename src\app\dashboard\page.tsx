"use client";

import React from "react";
import { useAuth } from "@/lib/auth/AuthContext";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import { Container, Typography, Card, CardContent, Grid, Box, Chip } from "@mui/material";
import { Dashboard as DashboardIcon, Person, Email, CalendarToday } from "@mui/icons-material";

function DashboardContent() {
  const { user } = useAuth();

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom>
          <DashboardIcon sx={{ mr: 2, verticalAlign: "middle" }} />
          Dashboard
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Welcome to your personal dashboard, {user?.first_name || user?.username}!
        </Typography>
      </Box>

      <Grid container spacing={3}>
        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Profile Information
              </Typography>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Person color="primary" />
                  <Typography variant="body1">
                    {user?.first_name} {user?.last_name}
                  </Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Email color="primary" />
                  <Typography variant="body1">{user?.email}</Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <CalendarToday color="primary" />
                  <Typography variant="body1">Member since: {user?.date_joined ? new Date(user.date_joined).toLocaleDateString() : "N/A"}</Typography>
                </Box>
                <Box>
                  <Chip label={user?.is_active ? "Active" : "Inactive"} color={user?.is_active ? "success" : "error"} size="small" />
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid size={{ xs: 12, md: 6 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <Typography variant="body2" color="text.secondary">
                This is a protected page that requires authentication. You can only see this content because you are logged in.
              </Typography>
              <Box mt={2}>
                <Typography variant="body2">• Create new projects</Typography>
                <Typography variant="body2">• Plan your weekly focus</Typography>
                <Typography variant="body2">• Track daily outcomes</Typography>
                <Typography variant="body2">• Review your progress</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid size={{ xs: 12 }}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Getting Started
              </Typography>
              <Typography variant="body1" paragraph>
                Welcome to the Agile Life Results System! This dashboard is your central hub for managing your life goals and tracking your progress.
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Authentication is working correctly - you can see this protected content because you&apos;re logged in. Try logging out and accessing
                this page again to see the protection in action.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
}

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <DashboardContent />
    </ProtectedRoute>
  );
}
