import { NextRequest } from 'next/server'
import { prisma } from '@/lib/database/prisma'
import { verifyToken, extractTokenFromHeader, JWTPayload } from '@/lib/auth/jwt'
import { createUnauthorizedResponse, createNotFoundResponse } from '@/lib/api/responses'

export interface AuthenticatedUser {
  id: string
  email: string
  username: string
  first_name: string
  last_name: string
  is_active: boolean
  is_staff: boolean
  is_superuser: boolean
  date_joined: Date
  last_login: Date | null
}

export interface AuthResult {
  success: boolean
  user?: AuthenticatedUser
  error?: any
}

export async function authenticateRequest(request: NextRequest): Promise<AuthResult> {
  try {
    // Extract token from Authorization header
    const authHeader = request.headers.get('authorization')
    const token = extractTokenFromHeader(authHeader)
    
    if (!token) {
      return { success: false, error: createUnauthorizedResponse() }
    }

    // Verify JWT token
    const payload = verifyToken(token)
    if (!payload) {
      return { success: false, error: createUnauthorizedResponse() }
    }

    // Find user in database
    const user = await prisma.auth_user.findUnique({
      where: { 
        id: payload.userId,
        is_active: true
      },
      select: {
        id: true,
        email: true,
        username: true,
        first_name: true,
        last_name: true,
        is_active: true,
        is_staff: true,
        is_superuser: true,
        date_joined: true,
        last_login: true,
      }
    })

    if (!user) {
      return { success: false, error: createNotFoundResponse('User') }
    }

    return { success: true, user: user as AuthenticatedUser }

  } catch (error) {
    console.error('Authentication error:', error)
    return { success: false, error: createUnauthorizedResponse() }
  }
}

export function requireAuth(handler: (request: NextRequest, user: AuthenticatedUser) => Promise<Response>) {
  return async (request: NextRequest) => {
    const authResult = await authenticateRequest(request)
    
    if (!authResult.success || !authResult.user) {
      return authResult.error
    }

    return handler(request, authResult.user)
  }
}
