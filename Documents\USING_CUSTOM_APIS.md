# Using Custom APIs - Complete Developer Guide

## 🚀 Overview

The Custom Fields system provides Notion-like flexibility for extending any work item with user-defined fields. This system is built with Next.js API routes and Prisma ORM, providing a modern full-stack solution with universal field definitions that work across all work item types. This guide covers everything from basic CRUD operations to advanced frontend integration patterns.

## 📋 Table of Contents

1. [Custom Field Types](#custom-field-types)
2. [CRUD Operations for Custom Field Definitions](#crud-operations-for-custom-field-definitions)
3. [Assigning Custom Fields to Projects/Outcomes](#assigning-custom-fields-to-projectsoutcomes)
4. [Managing Choice Options](#managing-choice-options)
5. [Frontend Integration Patterns](#frontend-integration-patterns)
6. [Drag & Drop Sorting](#drag--drop-sorting)
7. [Error Handling](#error-handling)
8. [Performance Optimization](#performance-optimization)

---

## 1. Custom Field Types

### Supported Field Types

| Field Type      | Description        | Value Format                | Use Cases                         |
| --------------- | ------------------ | --------------------------- | --------------------------------- |
| `TEXT`          | Single line text   | `string`                    | Names, titles, short descriptions |
| `TEXTAREA`      | Multi-line text    | `string`                    | Long descriptions, notes          |
| `NUMBER`        | Numeric values     | `number`                    | Budgets, scores, quantities       |
| `BOOLEAN`       | True/false         | `boolean`                   | Flags, yes/no questions           |
| `DATE`          | Date picker        | Multiple formats accepted\* | Deadlines, milestones             |
| `DATETIME`      | Date and time      | `"YYYY-MM-DDTHH:MM:SSZ"`    | Appointments, timestamps          |
| `EMAIL`         | Email validation   | `string`                    | Contact information               |
| `URL`           | URL validation     | `string`                    | Links, references                 |
| `PHONE`         | Phone number       | `string`                    | Contact numbers                   |
| `SINGLE_SELECT` | Dropdown selection | `choice_option_id`          | Priority, status, category        |
| `MULTI_SELECT`  | Multiple choices   | `[choice_option_id, ...]`   | Tags, multiple categories         |

**\*DATE Field Accepted Formats:**

- `"YYYY-MM-DD"` (ISO format, recommended)
- `"MM/DD/YYYY"` (US format)
- `"MM/DD/YY"` (US format with 2-digit year)
- `"DD/MM/YYYY"` (European format)
- `"DD/MM/YY"` (European format with 2-digit year)
- `"YYYY/MM/DD"` (Alternative ISO-like format)
- `"MM-DD-YYYY"` (US format with dashes)
- `"DD-MM-YYYY"` (European format with dashes)

All date formats are automatically converted to ISO format (`YYYY-MM-DD`) for storage.

### Universal Field Definitions

Custom field definitions are now universal and can be applied to any work item type (Projects, Outcomes, Weekly Plan Focus, etc.). This provides maximum flexibility and reusability across the entire application.

---

## 2. CRUD Operations for Custom Field Definitions

### 2.1 Create Custom Field Definition

**Endpoint:** `POST /api/workitems/custom-fields/`

#### Basic Field Example (Text)

```javascript
const createTextField = async () => {
  const response = await fetch("/api/workitems/custom-fields", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${YOUR_JWT_TOKEN}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      name: "Project Description",
      field_type: "TEXT",
      is_required: false,
      sort_order: 1,
    }),
  });

  return await response.json();
};
```

#### Choice Field Example (Single Select)

```javascript
const createPriorityField = async () => {
  const response = await fetch("/api/workitems/custom-fields", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${YOUR_JWT_TOKEN}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      name: "Priority Level",
      field_type: "SINGLE_SELECT",
      is_required: false,
      sort_order: 1,
      choice_options: [
        {
          value: "Critical",
          color: "#e74c3c",
          sort_order: 1,
        },
        {
          value: "High",
          color: "#f39c12",
          sort_order: 2,
        },
        {
          value: "Medium",
          color: "#3498db",
          sort_order: 3,
        },
        {
          value: "Low",
          color: "#95a5a6",
          sort_order: 4,
        },
      ],
    }),
  });

  return await response.json();
};
```

### 2.2 List Custom Field Definitions

**Endpoint:** `GET /api/workitems/custom-fields`

```javascript
const getCustomFields = async () => {
  const response = await fetch("/api/workitems/custom-fields", {
    headers: {
      Authorization: `Bearer ${YOUR_JWT_TOKEN}`,
    },
  });

  return await response.json();
};

// Usage example
const allFields = await getCustomFields();
```

### 2.3 Update Custom Field Definition

**Endpoint:** `PUT /api/workitems/custom-fields/{id}`

```javascript
const updateCustomField = async (fieldId, updates) => {
  const response = await fetch(`/api/workitems/custom-fields/${fieldId}`, {
    method: "PUT",
    headers: {
      Authorization: `Bearer ${YOUR_JWT_TOKEN}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify(updates),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`Failed to update custom field: ${JSON.stringify(errorData)}`);
  }

  return await response.json();
};

// Example: Update field name and add new choice option
await updateCustomField("field-uuid", {
  name: "Updated Priority Level",
  field_type: "SINGLE_SELECT",
  is_required: true,
  sort_order: 1,
  choice_options: [
    // Include existing options with their IDs
    {
      id: "existing-choice-uuid-1", // IMPORTANT: Include ID for existing options
      value: "Urgent",
      color: "#ff0000",
      sort_order: 0,
    },
    {
      id: "existing-choice-uuid-2",
      value: "High",
      color: "#ff8800",
      sort_order: 1,
    },
    {
      // New option without ID - will be created
      value: "Critical",
      color: "#8b0000",
      sort_order: 2,
    },
  ],
});
```

**🚨 IMPORTANT NOTES FOR CHOICE OPTIONS:**

- **Include IDs for existing options**: When updating choice options, always include the `id` field for existing options to prevent duplicate key errors
- **Omit IDs for new options**: New choice options should not include an `id` field - they will be auto-generated
- **Value uniqueness**: Each choice option must have a unique `value` within the same field definition
- **Complete replacement**: The `choice_options` array completely replaces existing options - any omitted options will be deleted
- **Error handling**: Always check response status and handle validation errors appropriately

### 2.4 Delete Custom Field Definition

**Endpoint:** `DELETE /api/workitems/custom-fields/{id}`

```javascript
const deleteCustomField = async (fieldId) => {
  const response = await fetch(`/api/workitems/custom-fields/${fieldId}`, {
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${YOUR_JWT_TOKEN}`,
    },
  });

  return response.status === 204; // Success
};
```

---

## 3. Assigning Custom Fields to Projects/Outcomes

### 3.1 Creating Projects with Custom Fields

**Endpoint:** `POST /api/workitems/projects`

```javascript
const createProjectWithCustomFields = async () => {
  const response = await fetch("/api/workitems/projects", {
    method: "POST",
    headers: {
      Authorization: `Bearer ${YOUR_JWT_TOKEN}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      name: "New Project",
      description: "Project description",
      life_aspect: "life-aspect-uuid",
      custom_field_inputs: [
        {
          definition_id: "priority-field-uuid",
          value: "high-priority-option-uuid",
        },
        {
          definition_id: "budget-field-uuid",
          value: 5000,
        },
        {
          definition_id: "due-date-field-uuid",
          value: "2024-12-31", // ISO format (recommended)
          // Alternative formats also work:
          // value: "12/31/2024", // US format
          // value: "12/31/24",   // US format with 2-digit year
          // value: "31/12/2024", // European format
        },
        {
          definition_id: "tags-field-uuid",
          value: ["tag1-uuid", "tag2-uuid"],
        },
      ],
    }),
  });

  return await response.json();
};
```

### 3.2 Updating Custom Field Values

**Endpoint:** `PATCH /api/workitems/projects/{id}`

```javascript
const updateProjectCustomFields = async (projectId, customFieldUpdates) => {
  const response = await fetch(`/api/workitems/projects/${projectId}`, {
    method: "PATCH",
    headers: {
      Authorization: `Bearer ${YOUR_JWT_TOKEN}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      custom_field_inputs: customFieldUpdates,
    }),
  });

  return await response.json();
};

// Example: Update only specific fields (additive update)
await updateProjectCustomFields("project-uuid", [
  {
    definition_id: "priority-field-uuid",
    value: "critical-priority-option-uuid",
  },
  {
    definition_id: "budget-field-uuid",
    value: 7500,
  },
]);
```

### 3.3 Reading Custom Field Values

When you fetch projects/outcomes, custom fields are automatically included in the `resolved_custom_fields` array:

```javascript
const getProjectWithCustomFields = async (projectId) => {
  const response = await fetch(`/api/workitems/projects/${projectId}`, {
    headers: {
      Authorization: `Bearer ${YOUR_JWT_TOKEN}`,
    },
  });

  const project = await response.json();

  // Access resolved custom fields
  project.resolved_custom_fields.forEach((field) => {
    console.log(`${field.name}: ${field.display_value}`);

    // For choice fields, you get rich objects
    if (field.field_type === "SINGLE_SELECT" && field.value) {
      console.log(`Color: ${field.value.color}`);
      console.log(`Choice ID: ${field.value.id}`);
    }
  });

  return project;
};
```

### 3.4 Removing Custom Field Values

To clear a custom field value, send `null` as the value:

```javascript
const clearCustomField = async (projectId, fieldDefinitionId) => {
  await updateProjectCustomFields(projectId, [
    {
      definition_id: fieldDefinitionId,
      value: null,
    },
  ]);
};
```

---

## 4. Managing Choice Options

### 4.1 Adding Choice Options

Choice options are managed as part of the custom field definition. To add new options:

```javascript
const addChoiceOption = async (fieldId, existingField, newOption) => {
  const updatedChoiceOptions = [
    ...existingField.choice_options,
    {
      value: newOption.value,
      color: newOption.color,
      sort_order: existingField.choice_options.length,
    },
  ];

  return await updateCustomField(fieldId, {
    ...existingField,
    choice_options: updatedChoiceOptions,
  });
};
```

### 4.2 Reordering Choice Options

```javascript
const reorderChoiceOptions = async (fieldId, field, newOrder) => {
  const reorderedOptions = newOrder.map((optionId, index) => {
    const option = field.choice_options.find((opt) => opt.id === optionId);
    return {
      ...option,
      sort_order: index,
    };
  });

  return await updateCustomField(fieldId, {
    ...field,
    choice_options: reorderedOptions,
  });
};
```

### 4.3 Deleting Choice Options

```javascript
const deleteChoiceOption = async (fieldId, field, optionIdToDelete) => {
  const filteredOptions = field.choice_options
    .filter((opt) => opt.id !== optionIdToDelete)
    .map((opt, index) => ({
      ...opt,
      sort_order: index,
    }));

  return await updateCustomField(fieldId, {
    ...field,
    choice_options: filteredOptions,
  });
};
```

---

## 5. Frontend Integration Patterns

### 5.1 Dynamic Form Generation

```javascript
const generateCustomFieldForm = (customFields, existingValues = {}) => {
  return customFields
    .map((field) => {
      const existingValue = existingValues[field.id];

      switch (field.field_type) {
        case "TEXT":
        case "TEXTAREA":
        case "EMAIL":
        case "URL":
          return {
            type: field.field_type.toLowerCase(),
            name: field.id,
            label: field.name,
            required: field.is_required,
            value: existingValue || "",
            placeholder: `Enter ${field.name.toLowerCase()}`,
          };

        case "NUMBER":
          return {
            type: "number",
            name: field.id,
            label: field.name,
            required: field.is_required,
            value: existingValue || "",
            step: "any",
          };

        case "BOOLEAN":
          return {
            type: "checkbox",
            name: field.id,
            label: field.name,
            required: field.is_required,
            checked: existingValue || false,
          };

        case "DATE":
          return {
            type: "date",
            name: field.id,
            label: field.name,
            required: field.is_required,
            value: existingValue || "",
          };

        case "SINGLE_SELECT":
          return {
            type: "select",
            name: field.id,
            label: field.name,
            required: field.is_required,
            value: existingValue?.id || "",
            options: field.choice_options.map((opt) => ({
              value: opt.id,
              label: opt.value,
              color: opt.color,
            })),
          };

        case "MULTI_SELECT":
          return {
            type: "multi-select",
            name: field.id,
            label: field.name,
            required: field.is_required,
            value: existingValue?.map((v) => v.id) || [],
            options: field.choice_options.map((opt) => ({
              value: opt.id,
              label: opt.value,
              color: opt.color,
            })),
          };

        default:
          return null;
      }
    })
    .filter(Boolean);
};
```

### 5.2 Value Formatting for Display

```javascript
const formatCustomFieldValue = (field, value) => {
  if (!value) return "-";

  switch (field.field_type) {
    case "BOOLEAN":
      return value ? "Yes" : "No";

    case "DATE":
      return new Date(value).toLocaleDateString();

    case "DATETIME":
      return new Date(value).toLocaleString();

    case "NUMBER":
      return typeof value === "number" ? value.toLocaleString() : value;

    case "SINGLE_SELECT":
      return value.value; // Display the choice text

    case "MULTI_SELECT":
      return value.map((choice) => choice.value).join(", ");

    default:
      return value;
  }
};
```

### 5.3 Validation Helpers

```javascript
const validateCustomFieldValue = (field, value) => {
  const errors = [];

  // Required field validation
  if (field.is_required && (!value || value === "")) {
    errors.push(`${field.name} is required`);
    return errors;
  }

  // Skip validation for empty non-required fields
  if (!value || value === "") return errors;

  switch (field.field_type) {
    case "EMAIL":
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        errors.push(`${field.name} must be a valid email address`);
      }
      break;

    case "URL":
      try {
        new URL(value);
      } catch {
        errors.push(`${field.name} must be a valid URL`);
      }
      break;

    case "NUMBER":
      if (isNaN(Number(value))) {
        errors.push(`${field.name} must be a valid number`);
      }
      break;

    case "DATE":
      if (isNaN(Date.parse(value))) {
        errors.push(`${field.name} must be a valid date`);
      }
      break;
  }

  return errors;
};
```

---

## 6. Drag & Drop Sorting

### 6.1 Sorting Custom Field Definitions

```javascript
const updateFieldOrder = async (fields, draggedFieldId, newIndex) => {
  // Create new order array
  const reorderedFields = [...fields];
  const draggedFieldIndex = reorderedFields.findIndex((f) => f.id === draggedFieldId);
  const draggedField = reorderedFields.splice(draggedFieldIndex, 1)[0];
  reorderedFields.splice(newIndex, 0, draggedField);

  // Update sort_order for all affected fields
  const updates = reorderedFields.map((field, index) => ({
    ...field,
    sort_order: index,
  }));

  // Send batch updates
  const updatePromises = updates.map((field) => updateCustomField(field.id, { sort_order: field.sort_order }));

  await Promise.all(updatePromises);
  return reorderedFields;
};
```

### 6.2 Sorting Choice Options

```javascript
const updateChoiceOptionOrder = async (fieldId, field, draggedOptionId, newIndex) => {
  const reorderedOptions = [...field.choice_options];
  const draggedOptionIndex = reorderedOptions.findIndex((opt) => opt.id === draggedOptionId);
  const draggedOption = reorderedOptions.splice(draggedOptionIndex, 1)[0];
  reorderedOptions.splice(newIndex, 0, draggedOption);

  // Update sort_order
  const updatedOptions = reorderedOptions.map((option, index) => ({
    ...option,
    sort_order: index,
  }));

  return await updateCustomField(fieldId, {
    ...field,
    choice_options: updatedOptions,
  });
};
```

### 6.3 React DnD Example

```jsx
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";

const CustomFieldsList = ({ fields, onReorder }) => {
  const handleDragEnd = (result) => {
    if (!result.destination) return;

    const { source, destination } = result;
    if (source.index === destination.index) return;

    onReorder(result.draggableId, destination.index);
  };

  return (
    <DragDropContext onDragEnd={handleDragEnd}>
      <Droppable droppableId="custom-fields">
        {(provided) => (
          <div {...provided.droppableProps} ref={provided.innerRef}>
            {fields.map((field, index) => (
              <Draggable key={field.id} draggableId={field.id} index={index}>
                {(provided, snapshot) => (
                  <div
                    ref={provided.innerRef}
                    {...provided.draggableProps}
                    {...provided.dragHandleProps}
                    className={`field-item ${snapshot.isDragging ? "dragging" : ""}`}
                  >
                    <span className="drag-handle">⋮⋮</span>
                    <span className="field-name">{field.name}</span>
                    <span className="field-type">{field.field_type}</span>
                  </div>
                )}
              </Draggable>
            ))}
            {provided.placeholder}
          </div>
        )}
      </Droppable>
    </DragDropContext>
  );
};
```

---

## 7. Error Handling

### 7.1 Common Error Scenarios

```javascript
const handleCustomFieldErrors = (error, context) => {
  if (error.status === 400) {
    const errorData = error.data;

    // Field-level validation errors
    if (errorData.custom_field_inputs) {
      errorData.custom_field_inputs.forEach((fieldError, index) => {
        if (fieldError) {
          console.error(`Field ${index} error:`, fieldError);
          // Show user-friendly error message
        }
      });
    }

    // Definition-level errors
    if (errorData.name) {
      console.error("Field name error:", errorData.name);
    }

    if (errorData.choice_options) {
      console.error("Choice options error:", errorData.choice_options);
    }
  }

  if (error.status === 404) {
    console.error("Custom field definition not found");
    // Handle deleted field scenario
  }

  if (error.status === 403) {
    console.error("Permission denied - field belongs to another user");
  }
};
```

### 7.2 Graceful Degradation

```javascript
const safeGetCustomFieldValue = (resolvedFields, fieldName, defaultValue = null) => {
  const field = resolvedFields.find((f) => f.name === fieldName);
  return field?.value ?? defaultValue;
};

const renderCustomFieldSafely = (field) => {
  try {
    return formatCustomFieldValue(field, field.value);
  } catch (error) {
    console.warn(`Error rendering field ${field.name}:`, error);
    return "-";
  }
};
```

---

## 8. Performance Optimization

### 8.1 Caching Strategies

```javascript
class CustomFieldsCache {
  constructor() {
    this.cache = new Map();
    this.lastFetch = new Map();
    this.CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
  }

  async getCustomFields(targetModel) {
    const cacheKey = `fields_${targetModel}`;
    const now = Date.now();
    const lastFetch = this.lastFetch.get(cacheKey) || 0;

    if (this.cache.has(cacheKey) && now - lastFetch < this.CACHE_DURATION) {
      return this.cache.get(cacheKey);
    }

    const fields = await getCustomFields(targetModel);
    this.cache.set(cacheKey, fields);
    this.lastFetch.set(cacheKey, now);

    return fields;
  }

  invalidate(targetModel = null) {
    if (targetModel) {
      this.cache.delete(`fields_${targetModel}`);
      this.lastFetch.delete(`fields_${targetModel}`);
    } else {
      this.cache.clear();
      this.lastFetch.clear();
    }
  }
}

const customFieldsCache = new CustomFieldsCache();
```

### 8.2 Batch Operations

```javascript
const batchUpdateCustomFields = async (updates) => {
  const promises = updates.map(({ projectId, customFieldInputs }) => updateProjectCustomFields(projectId, customFieldInputs));

  try {
    const results = await Promise.allSettled(promises);
    const failures = results.filter((r) => r.status === "rejected");

    if (failures.length > 0) {
      console.warn(`${failures.length} updates failed:`, failures);
    }

    return results;
  } catch (error) {
    console.error("Batch update failed:", error);
    throw error;
  }
};
```

### 8.3 Optimistic Updates

```javascript
const useOptimisticCustomFieldUpdate = () => {
  const [fields, setFields] = useState([]);
  const [isUpdating, setIsUpdating] = useState(false);

  const updateFieldOptimistically = async (fieldId, updates) => {
    // Optimistic update
    const optimisticFields = fields.map((field) => (field.id === fieldId ? { ...field, ...updates } : field));
    setFields(optimisticFields);
    setIsUpdating(true);

    try {
      const updatedField = await updateCustomField(fieldId, updates);

      // Replace optimistic update with server response
      setFields(fields.map((field) => (field.id === fieldId ? updatedField : field)));
    } catch (error) {
      // Revert optimistic update
      setFields(fields);
      throw error;
    } finally {
      setIsUpdating(false);
    }
  };

  return { fields, updateFieldOptimistically, isUpdating };
};
```

---

## 🎯 Quick Reference

### Essential Endpoints

- **List Fields:** `GET /api/workitems/custom-fields`
- **Create Field:** `POST /api/workitems/custom-fields`
- **Update Field:** `PUT /api/workitems/custom-fields/{id}`
- **Delete Field:** `DELETE /api/workitems/custom-fields/{id}`
- **Assign to Project:** Include `custom_field_inputs` in project POST/PATCH
- **Assign to Outcome:** Include `custom_field_inputs` in outcome POST/PATCH

### Key Concepts

- **Definition vs Value:** Definitions are schemas, values are stored per project/outcome
- **Choice Fields:** Use UUIDs for option values, not text
- **Additive Updates:** PATCH operations merge with existing values
- **Resolved Fields:** API returns rich objects with definition details
- **Sort Order:** Controls display order in UI (0-based, non-unique allowed)

### Best Practices

1. **Cache field definitions** to avoid repeated API calls
2. **Use optimistic updates** for better UX
3. **Validate on frontend** before sending to API
4. **Handle deleted choice options** gracefully
5. **Implement drag & drop** for intuitive reordering
6. **Batch operations** when possible for performance
7. **Use TypeScript** for better type safety with field definitions

---

## 🔧 Recent Bug Fixes & Updates

### Fixed: 400 Bad Request on Project Update with DATE Custom Fields (December 2024)

**Issue:** Frontend was receiving persistent 400 Bad Request errors when updating projects with DATE custom field values, specifically when using common date formats like `"12/06/25"`.

**Root Cause:** The backend DATE field validation was overly strict, only accepting the exact ISO format `YYYY-MM-DD`. Frontend date pickers commonly send dates in formats like `MM/DD/YY` or `DD/MM/YYYY`, causing validation failures.

**Solution:** Enhanced the `validate_date_field()` function to accept multiple common date formats:

- `"YYYY-MM-DD"` (ISO format, recommended)
- `"MM/DD/YYYY"` and `"MM/DD/YY"` (US formats)
- `"DD/MM/YYYY"` and `"DD/MM/YY"` (European formats)
- `"YYYY/MM/DD"` (Alternative ISO-like format)
- `"MM-DD-YYYY"` and `"DD-MM-YYYY"` (Dash-separated formats)

**Impact:** The PATCH `/api/workitems/projects/{id}` endpoint now accepts flexible date formats while still storing dates in consistent ISO format.

**Frontend Benefits:**

- No need to convert date formats before sending to API
- Date pickers can send their native format directly
- Better user experience with fewer validation errors
- Backward compatibility maintained for existing ISO format usage

### Fixed: 500 Internal Server Error on Custom Field Updates (June 2025)

**Issue:** When updating custom field definitions with choice options, the API was returning a 500 Internal Server Error due to unique constraint violations.

**Root Cause:** The backend was attempting to create duplicate choice options when the frontend sent choice options without IDs or with the same values as existing options.

**Solution:** Enhanced the `_handle_choice_options()` method in the backend to:

- Match choice options by both ID and value
- Prevent duplicate key violations through smart matching logic
- Provide clear validation error messages instead of 500 errors
- Handle frontend edge cases where IDs may not be provided

**Impact:** The PUT `/api/workitems/custom-fields/{id}` endpoint now works reliably for all update scenarios.

**Frontend Recommendations:**

- Always include `id` fields for existing choice options when updating
- Handle validation errors gracefully with proper error messages
- Test choice option updates thoroughly in your frontend implementation

---

For more examples and advanced patterns, see the [API Documentation](API_DOCUMENTATION.md) and [Custom Fields Manager](http://localhost:8000/custom-fields-manager/) interface.
