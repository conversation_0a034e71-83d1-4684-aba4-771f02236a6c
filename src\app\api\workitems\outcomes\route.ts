import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/database/prisma";
import { verifyToken, extractTokenFromHeader } from "@/lib/auth/jwt";
import { validateCreateOutcome, ValidationError, OutcomeInput } from "@/lib/validation/outcomes";
import { resolveCustomFields, processCustomFieldInputs } from "@/lib/utils/customFieldResolver";
import { applyInheritanceWorkflow } from "@/lib/utils/customFieldInheritance";

// Helper function to authenticate request
async function authenticateRequest(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  const token = extractTokenFromHeader(authHeader);

  if (!token) {
    return { error: NextResponse.json({ message: "Authentication required" }, { status: 401 }) };
  }

  const payload = verifyToken(token);
  if (!payload) {
    return { error: NextResponse.json({ message: "Invalid or expired token" }, { status: 401 }) };
  }

  return { userId: payload.userId };
}

// Helper function to create validation error response
function createValidationErrorResponse(errors: ValidationError[]) {
  const errorMap: Record<string, string[]> = {};
  errors.forEach((error) => {
    if (!errorMap[error.field]) errorMap[error.field] = [];
    errorMap[error.field].push(error.message);
  });

  return NextResponse.json(
    {
      message: "Validation failed",
      errors: errorMap,
    },
    { status: 400 }
  );
}

// Helper function to extract legacy priority from custom fields
function extractLegacyPriority(resolvedCustomFields: any[]): { priority_level: string | null; priority_level_name: string | null } {
  // Look for a priority-related custom field
  const priorityField = resolvedCustomFields.find(
    (field) => field.name.toLowerCase().includes("priority") && field.field_type === "SINGLE_SELECT" && field.value
  );

  if (priorityField && priorityField.value) {
    return {
      priority_level: priorityField.value.id || null,
      priority_level_name: priorityField.value.value || null,
    };
  }

  return {
    priority_level: null,
    priority_level_name: null,
  };
}

// GET /api/workitems/outcomes/ - List outcomes
export async function GET(request: NextRequest) {
  try {
    console.log("GET /api/workitems/outcomes/ called");

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get("project_id");

    // Build where clause
    const where: any = {
      user_id: auth.userId,
    };

    if (projectId) {
      where.project_id = projectId;
    }

    // Fetch outcomes with relations
    const outcomes = await prisma.workitems_outcome.findMany({
      where,
      include: {
        project: {
          select: {
            id: true,
            name: true,
            life_aspect: {
              select: {
                id: true,
                name: true,
                color: true,
              },
            },
          },
        },
      },
      orderBy: [{ sort_order: "asc" }, { created_at: "asc" }],
    });

    // Resolve custom fields for each outcome and add legacy priority fields
    const outcomesWithCustomFields = await Promise.all(
      outcomes.map(async (outcome) => {
        const resolvedCustomFields = await resolveCustomFields(outcome.custom_field_values as Record<string, any>, "OUTCOME", auth.userId);

        // Extract legacy priority for hybrid support
        const legacyPriority = extractLegacyPriority(resolvedCustomFields);

        return {
          ...outcome,
          resolved_custom_fields: resolvedCustomFields,
          // Hybrid priority support - include both legacy and new fields
          priority_level: outcome.priority_level || legacyPriority.priority_level,
          priority_level_name: outcome.priority_level_name || legacyPriority.priority_level_name,
        };
      })
    );

    console.log(`Found ${outcomesWithCustomFields.length} outcomes for user ${auth.userId}`);

    return NextResponse.json(outcomesWithCustomFields);
  } catch (error) {
    console.error("Error fetching outcomes:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// POST /api/workitems/outcomes/ - Create outcome
export async function POST(request: NextRequest) {
  try {
    console.log("POST /api/workitems/outcomes/ called");

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Parse request body
    const body = (await request.json()) as OutcomeInput;
    console.log("Request body:", { ...body, custom_field_inputs: body.custom_field_inputs?.length || 0 });

    // Validate input
    const validationErrors = validateCreateOutcome(body);
    if (validationErrors.length > 0) {
      return createValidationErrorResponse(validationErrors);
    }

    // Verify project exists and belongs to user
    const project = await prisma.workitems_project.findFirst({
      where: {
        id: body.project_id,
        user_id: auth.userId,
      },
    });

    if (!project) {
      return NextResponse.json(
        {
          message: "Project not found or does not belong to user",
        },
        { status: 400 }
      );
    }

    // Process custom field inputs with inheritance from parent project
    let customFieldValues = {};
    try {
      customFieldValues = await applyInheritanceWorkflow(
        body.project_id, // For outcomes, the parent is the project they belong to
        auth.userId,
        body.custom_field_inputs || [],
        "OUTCOME"
      );
    } catch (error) {
      return NextResponse.json(
        {
          message: "Custom field processing failed",
          error: error instanceof Error ? error.message : "Unknown error",
        },
        { status: 400 }
      );
    }

    // Create outcome
    const outcome = await prisma.workitems_outcome.create({
      data: {
        user_id: auth.userId,
        name: body.name,
        description: body.description || null,
        project_id: body.project_id,
        custom_field_values: customFieldValues,
        sort_order: body.sort_order || 0,
      },
      include: {
        project: {
          select: {
            id: true,
            name: true,
            life_aspect: {
              select: {
                id: true,
                name: true,
                color: true,
              },
            },
          },
        },
      },
    });

    // Resolve custom fields
    const resolvedCustomFields = await resolveCustomFields(outcome.custom_field_values as Record<string, any>, "OUTCOME", auth.userId);

    // Extract legacy priority for hybrid support
    const legacyPriority = extractLegacyPriority(resolvedCustomFields);

    const outcomeWithCustomFields = {
      ...outcome,
      resolved_custom_fields: resolvedCustomFields,
      priority_level: outcome.priority_level || legacyPriority.priority_level,
      priority_level_name: outcome.priority_level_name || legacyPriority.priority_level_name,
    };

    console.log("Created outcome:", outcome.id);

    return NextResponse.json(outcomeWithCustomFields, { status: 201 });
  } catch (error) {
    console.error("Error creating outcome:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
