"use client";

import React, { useState, useEffect } from "react";
import { useAuth } from "@/lib/auth/AuthContext";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import { lifeAspectsService } from "@/lib/api/lifeAspectsService";
import { LifeAspect } from "@/lib/types/lifeAspects";
import { Container, Typography, Card, CardContent, Grid, Box, Button, CircularProgress, Alert, Chip, Divider, Stack } from "@mui/material";
import {
  Add as AddIcon,
  Psychology as MindIcon,
  FitnessCenter as BodyIcon,
  Favorite as EmotionsIcon,
  Work as CareerIcon,
  AccountBalance as FinanceIcon,
  People as RelationshipsIcon,
  SportsEsports as FunIcon,
  Spa as SpiritualIcon,
  VolunteerActivism as ContributionIcon,
  Category as DefaultIcon,
} from "@mui/icons-material";

// Icon mapping for default life aspects
const getLifeAspectIcon = (name: string) => {
  const iconMap: Record<string, React.ReactNode> = {
    Mind: <MindIcon />,
    Body: <BodyIcon />,
    Emotions: <EmotionsIcon />,
    Career: <CareerIcon />,
    Finance: <FinanceIcon />,
    Relationships: <RelationshipsIcon />,
    "Fun & Recreation": <FunIcon />,
    Spiritual: <SpiritualIcon />,
    "Contribution & Legacy": <ContributionIcon />,
  };

  return iconMap[name] || <DefaultIcon />;
};

function LifeAspectsContent() {
  const { isAuthenticated } = useAuth();
  const [lifeAspects, setLifeAspects] = useState<LifeAspect[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch life aspects when component mounts
  useEffect(() => {
    const fetchLifeAspects = async () => {
      if (!isAuthenticated) return;

      try {
        setIsLoading(true);
        setError(null);
        const aspects = await lifeAspectsService.getLifeAspects();
        setLifeAspects(aspects);
      } catch (err: unknown) {
        console.error("Failed to fetch life aspects:", err);
        const errorMessage = err instanceof Error ? err.message : "Failed to load life aspects. Please try again.";
        setError(errorMessage);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLifeAspects();
  }, [isAuthenticated]);

  // Loading state
  if (isLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <Stack spacing={2} alignItems="center">
            <CircularProgress size={48} />
            <Typography variant="body1" color="text.secondary">
              Loading your life aspects...
            </Typography>
          </Stack>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header Section */}
      <Box mb={4}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 500 }}>
          My Life Aspects
        </Typography>
        <Typography variant="body1" color="text.secondary" paragraph>
          Life aspects help you organize and balance different areas of your life. Each aspect represents a key area where you can set goals and track
          progress.
        </Typography>

        {/* Add New Button */}
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          size="large"
          sx={{
            borderRadius: 2,
            textTransform: "none",
            px: 3,
            py: 1.5,
          }}
          disabled // Will be functional in future implementation
        >
          Add New Life Aspect
        </Button>
      </Box>

      {/* Error State */}
      {error && (
        <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Life Aspects Grid */}
      {lifeAspects.length === 0 ? (
        // Empty State
        <Card sx={{ borderRadius: 3, textAlign: "center", py: 6 }}>
          <CardContent>
            <Box mb={3}>
              <DefaultIcon sx={{ fontSize: 64, color: "text.secondary", mb: 2 }} />
            </Box>
            <Typography variant="h6" gutterBottom>
              No Life Aspects Yet
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              You haven&apos;t defined any life aspects yet. Get started by adding your first one!
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Life aspects help you organize different areas of your life like career, health, relationships, and personal growth.
            </Typography>
          </CardContent>
        </Card>
      ) : (
        // Life Aspects Grid
        <Grid container spacing={3}>
          {lifeAspects
            .sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0)) // Sort by sort_order
            .map((aspect) => (
              <Grid size={{ xs: 12, sm: 6, md: 4 }} key={aspect.id}>
                <Card
                  sx={{
                    borderRadius: 3,
                    transition: "all 0.2s ease-in-out",
                    "&:hover": {
                      transform: "translateY(-2px)",
                      boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.15)",
                    },
                    height: "100%",
                    display: "flex",
                    flexDirection: "column",
                  }}
                >
                  <CardContent sx={{ flexGrow: 1, p: 3 }}>
                    {/* Icon and Title */}
                    <Box display="flex" alignItems="center" mb={2}>
                      <Box
                        sx={{
                          color: "primary.main",
                          mr: 2,
                          display: "flex",
                          alignItems: "center",
                        }}
                      >
                        {getLifeAspectIcon(aspect.name)}
                      </Box>
                      <Typography variant="h6" component="h3" sx={{ fontWeight: 500 }}>
                        {aspect.name}
                      </Typography>
                    </Box>

                    {/* Description */}
                    {aspect.description && (
                      <Typography variant="body2" color="text.secondary" paragraph sx={{ mb: 2 }}>
                        {aspect.description}
                      </Typography>
                    )}

                    <Divider sx={{ my: 2 }} />

                    {/* Metadata */}
                    <Stack direction="row" spacing={1} alignItems="center">
                      <Chip label={`Order: ${aspect.sort_order}`} size="small" variant="outlined" sx={{ fontSize: "0.75rem" }} />
                      <Typography variant="caption" color="text.secondary">
                        Created: {new Date(aspect.created_at).toLocaleDateString()}
                      </Typography>
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
            ))}
        </Grid>
      )}

      {/* Summary Info */}
      {lifeAspects.length > 0 && (
        <Box mt={4} p={3} sx={{ bgcolor: "grey.50", borderRadius: 2 }}>
          <Typography variant="body2" color="text.secondary" align="center">
            You have <strong>{lifeAspects.length}</strong> life aspect{lifeAspects.length !== 1 ? "s" : ""} defined. Use these to organize your goals
            and track progress across different areas of your life.
          </Typography>
        </Box>
      )}
    </Container>
  );
}

export default function LifeAspectsPage() {
  return (
    <ProtectedRoute>
      <LifeAspectsContent />
    </ProtectedRoute>
  );
}
