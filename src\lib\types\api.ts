// Common API response types
export interface ApiResponse<T = unknown> {
  data: T;
  message?: string;
  status: number;
}

export interface ApiError {
  message: string;
  status: number;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> {
  count: number;
  next: string | null;
  previous: string | null;
  results: T[];
}

// Common request/response patterns
export interface CreateResponse {
  id: number;
  created_at: string;
}

export interface UpdateResponse {
  updated_at: string;
}

export interface DeleteResponse {
  message: string;
}
