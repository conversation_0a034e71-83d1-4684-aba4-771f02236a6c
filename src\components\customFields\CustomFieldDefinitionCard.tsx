"use client";

import React, { useState } from "react";
import { <PERSON>, CardContent, Typo<PERSON>, Box, Chip, IconButton, Switch, FormControlLabel, <PERSON>ltip, alpha } from "@mui/material";
import { Settings as SettingsIcon, Delete as DeleteIcon, Edit as EditIcon, Star as StarIcon } from "@mui/icons-material";
import { CustomFieldDefinition, FIELD_TYPES, FieldTypeInfo } from "@/lib/types/customFields";

interface CustomFieldDefinitionCardProps {
  definition: CustomFieldDefinition;
  onEdit: (definition: CustomFieldDefinition) => void;
  onDelete: (definition: CustomFieldDefinition) => void;
  onToggleRequired: (definition: CustomFieldDefinition) => void;
}

const CustomFieldDefinitionCard: React.FC<CustomFieldDefinitionCardProps> = ({ definition, onEdit, onDelete, onToggleRequired }) => {
  const [isHovered, setIsHovered] = useState(false);

  // Get field type information
  const fieldTypeInfo: FieldTypeInfo | undefined = FIELD_TYPES.find((type) => type.type === definition.field_type);

  // Handle settings click
  const handleSettingsClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit(definition);
  };

  // Handle delete click
  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete(definition);
  };

  // Handle required toggle
  const handleRequiredToggle = (e: React.MouseEvent) => {
    e.stopPropagation();
    onToggleRequired(definition);
  };

  return (
    <Card
      sx={{
        position: "relative",
        cursor: "pointer",
        transition: "all 0.2s ease-in-out",
        "&:hover": {
          transform: "translateY(-2px)",
          boxShadow: (theme) => theme.shadows[8],
        },
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={() => onEdit(definition)}
    >
      <CardContent sx={{ pb: 2 }}>
        {/* Header with title and settings icon */}
        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
          <Typography
            variant="h6"
            component="h3"
            sx={{
              fontWeight: 500,
              fontSize: "1.1rem",
              lineHeight: 1.3,
              flex: 1,
              mr: 1,
            }}
          >
            {definition.name}
          </Typography>

          {/* Settings Icon - Only visible on hover */}
          <IconButton
            size="small"
            onClick={handleSettingsClick}
            sx={{
              opacity: isHovered ? 1 : 0,
              transition: "opacity 0.2s ease-in-out",
              color: "text.secondary",
              "&:hover": {
                color: "primary.main",
                backgroundColor: (theme) => alpha(theme.palette.primary.main, 0.1),
              },
            }}
          >
            <SettingsIcon fontSize="small" />
          </IconButton>
        </Box>

        {/* Field Type Information */}
        <Box mb={2}>
          <Chip
            label={fieldTypeInfo?.label || definition.field_type}
            size="small"
            variant="outlined"
            sx={{
              borderRadius: 1,
              fontSize: "0.75rem",
              height: 24,
            }}
          />
        </Box>

        {/* Choice Options for SELECT fields */}
        {definition.choice_options && definition.choice_options.length > 0 && (
          <Box mb={2}>
            <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: "block" }}>
              Options:
            </Typography>
            <Box display="flex" flexWrap="wrap" gap={0.5}>
              {definition.choice_options
                .sort((a, b) => a.sort_order - b.sort_order)
                .slice(0, 4) // Show max 4 options
                .map((option) => (
                  <Chip
                    key={option.id}
                    label={
                      <Box display="flex" alignItems="center" gap={0.5}>
                        {option.is_default && (
                          <StarIcon
                            sx={{
                              fontSize: "0.7rem",
                              color: "white",
                              filter: "drop-shadow(0px 1px 1px rgba(0,0,0,0.3))",
                            }}
                          />
                        )}
                        {option.value}
                      </Box>
                    }
                    size="small"
                    sx={{
                      backgroundColor: option.color,
                      color: "white",
                      fontSize: "0.7rem",
                      height: 20,
                      "& .MuiChip-label": {
                        px: 1,
                        display: "flex",
                        alignItems: "center",
                      },
                    }}
                  />
                ))}
              {definition.choice_options.length > 4 && (
                <Chip
                  label={`+${definition.choice_options.length - 4} more`}
                  size="small"
                  variant="outlined"
                  sx={{
                    fontSize: "0.7rem",
                    height: 20,
                    "& .MuiChip-label": {
                      px: 1,
                    },
                  }}
                />
              )}
            </Box>
          </Box>
        )}

        {/* Field Type Description for non-SELECT fields */}
        {(!definition.choice_options || definition.choice_options.length === 0) && fieldTypeInfo && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2, fontSize: "0.8rem" }}>
            {fieldTypeInfo.description}
          </Typography>
        )}

        {/* Required Toggle */}
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <FormControlLabel
            control={<Switch checked={definition.is_required} onChange={handleRequiredToggle} size="small" onClick={(e) => e.stopPropagation()} />}
            label={
              <Typography variant="caption" color="text.secondary">
                Required
              </Typography>
            }
            sx={{ m: 0 }}
          />

          {/* Quick Actions - Only visible on hover */}
          <Box
            sx={{
              opacity: isHovered ? 1 : 0,
              transition: "opacity 0.2s ease-in-out",
              display: "flex",
              gap: 0.5,
            }}
          >
            <Tooltip title="Edit">
              <IconButton
                size="small"
                onClick={handleSettingsClick}
                sx={{
                  color: "text.secondary",
                  "&:hover": {
                    color: "primary.main",
                    backgroundColor: (theme) => alpha(theme.palette.primary.main, 0.1),
                  },
                }}
              >
                <EditIcon fontSize="small" />
              </IconButton>
            </Tooltip>

            <Tooltip title="Delete">
              <IconButton
                size="small"
                onClick={handleDeleteClick}
                sx={{
                  color: "text.secondary",
                  "&:hover": {
                    color: "error.main",
                    backgroundColor: (theme) => alpha(theme.palette.error.main, 0.1),
                  },
                }}
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        </Box>
      </CardContent>
    </Card>
  );
};

export default CustomFieldDefinitionCard;
