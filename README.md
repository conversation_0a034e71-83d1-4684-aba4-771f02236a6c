# Minimisia Frontend

A modern, responsive project management application built with Next.js, TypeScript, and Material-UI.

## 🚀 Features

### Core Functionality

- **Project Hierarchy Management**: Organize projects in hierarchical structures with Life Aspects
- **Custom Fields System**: Flexible custom field definitions with multiple field types
- **User Authentication**: Secure JWT-based authentication system
- **Responsive Design**: Mobile-first design with Material-UI components
- **Real-time Updates**: Dynamic project management with instant feedback

### Project Management

- **Hierarchical Project Structure**: Multi-level project organization
- **Life Aspects**: Categorize projects by life areas (Work, Personal, Health, etc.)
- **Project Overview Table**: Comprehensive table view with sticky headers
- **Contextual Actions**: Right-click menus and inline editing
- **Progress Tracking**: Visual progress indicators and completion tracking

### Custom Fields

- **Multiple Field Types**: Text, Number, Date, Boolean, Single/Multi-Select
- **Choice Options**: Colored choice options for select fields
- **Field Validation**: Type-specific validation and error handling
- **User Preferences**: Customizable field visibility and pinning
- **Badge Display**: Colored badges for visual field representation

## 🛠️ Tech Stack

- **Framework**: Next.js 15.3.2
- **Language**: TypeScript
- **UI Library**: Material-UI (MUI) v7
- **Database**: PostgreSQL with Prisma ORM v6
- **Authentication**: JWT tokens with bcrypt
- **Styling**: Material-UI theme system + Tailwind CSS v4
- **State Management**: React hooks and context
- **HTTP Client**: Axios
- **Drag & Drop**: @hello-pangea/dnd

## 📁 Project Structure

```
src/
├── app/                    # Next.js app router pages
│   ├── api/               # API routes
│   ├── login/             # Authentication pages
│   ├── register/          # User registration
│   ├── overview/          # Main project overview
│   └── settings/          # User settings
├── components/            # Reusable React components
│   ├── auth/              # Authentication components
│   ├── common/            # Shared UI components
│   ├── customFields/      # Custom field components
│   ├── projects/          # Project-related components
│   └── settings/          # Settings components
├── lib/                   # Utility libraries
│   ├── api/               # API service functions
│   ├── auth/              # Authentication utilities
│   ├── database/          # Database configuration
│   ├── types/             # TypeScript type definitions
│   └── utils/             # Helper functions
└── generated/             # Generated Prisma client
```

## 🚦 Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL database
- npm or yarn

### Installation

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd minimisia_frontend_only
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create `.env.local` file:

   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/minimisia"
   JWT_SECRET="your-jwt-secret-key"
   NEXTAUTH_SECRET="your-nextauth-secret"
   ```

4. **Set up the database**

   ```bash
   npx prisma generate
   npx prisma db push
   ```

5. **Run the development server**

   ```bash
   npm run dev
   ```

6. **Open the application**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📖 Documentation

Essential documentation is available in the `Documents/` folder:

- **[STYLE_GUIDE.md](Documents/STYLE_GUIDE.md)**: UI/UX design guidelines and component standards
- **[USING_CUSTOM_APIS.md](Documents/USING_CUSTOM_APIS.md)**: API usage guide and examples
- **[CUSTOM_FIELDS_IMPLEMENTATION_SUMMARY.md](Documents/CUSTOM_FIELDS_IMPLEMENTATION_SUMMARY.md)**: Custom fields system overview
- **[USER_PREFERENCES_API.md](Documents/USER_PREFERENCES_API.md)**: User preferences system documentation

## 🎯 Key Features Implemented

### ✅ Authentication System

- User registration and login
- JWT token-based authentication
- Protected routes and middleware
- Password hashing with bcrypt

### ✅ Project Management

- Hierarchical project structure
- Life Aspects categorization
- Project CRUD operations
- Contextual project creation
- Progress tracking with outcomes

### ✅ Custom Fields System

- Dynamic field definitions
- Multiple field types support
- Choice options with colors
- Field validation and error handling
- User preferences for field visibility

### ✅ User Interface

- Responsive Material-UI design
- Hierarchical table with sticky headers
- Custom field badges with colors
- Interactive project management
- Contextual menus and actions

## 🔧 Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

### Code Quality

- TypeScript for type safety
- ESLint for code quality
- Prisma for database type safety
- Material-UI for consistent design

## 🎨 Design System

The application follows a consistent design system based on Material-UI:

- **Primary Color**: Blue (#1976d2)
- **Typography**: Roboto font family
- **Spacing**: 8px base unit
- **Responsive Breakpoints**: Mobile-first approach

## 📱 Responsive Design

- **Mobile**: Optimized for touch interactions
- **Tablet**: Adaptive layout with collapsible sidebars
- **Desktop**: Full-featured interface with hover states

## 🔐 Security

- JWT token authentication
- Password hashing with bcrypt
- Protected API routes
- Input validation and sanitization
- CORS configuration

## 📊 Performance

- Next.js optimizations
- Image optimization
- Code splitting
- Lazy loading
- Efficient database queries with Prisma

---

**Built with ❤️ using Next.js and Material-UI**
