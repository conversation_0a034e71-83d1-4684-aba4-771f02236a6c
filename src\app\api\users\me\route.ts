import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/database/prisma";
import { verifyToken, extractTokenFromHeader } from "@/lib/auth/jwt";

export async function GET(request: NextRequest) {
  try {
    console.log("User profile endpoint called");

    const authHeader = request.headers.get("authorization");
    console.log("Authorization header:", authHeader);

    if (!authHeader) {
      return NextResponse.json({ message: "Authentication required" }, { status: 401 });
    }

    // Extract token from Authorization header
    const token = extractTokenFromHeader(authHeader);
    if (!token) {
      return NextResponse.json({ message: "Invalid authorization header" }, { status: 401 });
    }

    // Verify JWT token
    const payload = verifyToken(token);
    if (!payload) {
      return NextResponse.json({ message: "Invalid or expired token" }, { status: 401 });
    }

    // Find user in database
    const user = await prisma.auth_user.findUnique({
      where: {
        id: payload.userId,
        is_active: true,
      },
      select: {
        id: true,
        email: true,
        username: true,
        first_name: true,
        last_name: true,
        is_active: true,
        is_staff: true,
        is_superuser: true,
        date_joined: true,
        last_login: true,
      },
    });

    if (!user) {
      return NextResponse.json({ message: "User not found" }, { status: 404 });
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error("Get user profile error:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
