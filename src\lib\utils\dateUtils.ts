/**
 * Date utility functions for custom fields
 * Handles conversion between display format and API format (YYYY-MM-DD)
 *
 * Note: As of December 2024, the backend accepts multiple date formats including DD/MM/YY,
 * but we convert to YYYY-MM-DD format as a best practice for consistency and reliability.
 *
 * Updated: Now supports the new professional format "E, d MMM, yy" (e.g., "Mon, 16 Jun, 25")
 */

import { format, parse, isValid } from "date-fns";

/**
 * Convert API date format (YYYY-MM-DD) to display format (DD/MM/YY)
 * @param apiDate - Date string in YYYY-MM-DD format
 * @returns Date string in DD/MM/YY format or empty string if invalid
 */
export const formatDateForDisplay = (apiDate: string | null | undefined): string => {
  if (!apiDate) return "";

  try {
    // Parse the API date (YYYY-MM-DD)
    const date = parse(apiDate, "yyyy-MM-dd", new Date());

    if (!isValid(date)) {
      console.warn("Invalid date format received:", apiDate);
      return "";
    }

    // Format as DD/MM/YY
    return format(date, "dd/MM/yy");
  } catch (error) {
    console.warn("Error formatting date for display:", error, apiDate);
    return "";
  }
};

/**
 * Convert display format (DD/MM/YY) to API format (YYYY-MM-DD)
 * @param displayDate - Date string in DD/MM/YY format
 * @returns Date string in YYYY-MM-DD format or null if invalid
 */
export const formatDateForApi = (displayDate: string | null | undefined): string | null => {
  if (!displayDate) return null;

  try {
    // Parse the display date (DD/MM/YY)
    const date = parse(displayDate, "dd/MM/yy", new Date());

    if (!isValid(date)) {
      console.warn("Invalid display date format:", displayDate);
      return null;
    }

    // Format as YYYY-MM-DD for API
    return format(date, "yyyy-MM-dd");
  } catch (error) {
    console.warn("Error formatting date for API:", error, displayDate);
    return null;
  }
};

/**
 * Convert Date object to API format (YYYY-MM-DD)
 * @param date - Date object
 * @returns Date string in YYYY-MM-DD format or null if invalid
 */
export const dateObjectToApiFormat = (date: Date | null | undefined): string | null => {
  if (!date || !isValid(date)) return null;

  try {
    return format(date, "yyyy-MM-dd");
  } catch (error) {
    console.warn("Error converting date object to API format:", error, date);
    return null;
  }
};

/**
 * Convert API date format (YYYY-MM-DD) to Date object
 * @param apiDate - Date string in YYYY-MM-DD format
 * @returns Date object or null if invalid
 */
export const apiDateToDateObject = (apiDate: string | null | undefined): Date | null => {
  if (!apiDate) return null;

  try {
    // Handle both YYYY-MM-DD format and ISO date strings from backend
    let date: Date;

    if (apiDate.includes("T")) {
      // ISO format (e.g., "2025-06-17T00:00:00.000Z")
      date = new Date(apiDate);
    } else {
      // YYYY-MM-DD format
      date = parse(apiDate, "yyyy-MM-dd", new Date());
    }

    return isValid(date) ? date : null;
  } catch (error) {
    console.warn("Error converting API date to Date object:", error, apiDate);
    return null;
  }
};

/**
 * Validate if a string is in DD/MM/YY format
 * @param dateString - Date string to validate
 * @returns boolean indicating if the format is valid
 */
export const isValidDisplayDateFormat = (dateString: string): boolean => {
  if (!dateString) return false;

  try {
    const date = parse(dateString, "dd/MM/yy", new Date());
    return isValid(date);
  } catch {
    return false;
  }
};

/**
 * Validate if a string is in YYYY-MM-DD format
 * @param dateString - Date string to validate
 * @returns boolean indicating if the format is valid
 */
export const isValidApiDateFormat = (dateString: string): boolean => {
  if (!dateString) return false;

  try {
    const date = parse(dateString, "yyyy-MM-dd", new Date());
    return isValid(date);
  } catch {
    return false;
  }
};

/**
 * Format date to professional display format (E, d MMM, yy)
 * @param date - Date object, API date string, or display date string
 * @returns Formatted string like "Mon, 16 Jun, 25" or empty string if invalid
 */
export const formatDateProfessional = (date: Date | string | null | undefined): string => {
  if (!date) return "";

  try {
    let dateObj: Date;

    if (date instanceof Date) {
      dateObj = date;
    } else if (typeof date === "string") {
      // Try to parse as API format first (YYYY-MM-DD)
      if (isValidApiDateFormat(date)) {
        dateObj = parse(date, "yyyy-MM-dd", new Date());
      } else if (isValidDisplayDateFormat(date)) {
        // Try to parse as display format (DD/MM/YY)
        dateObj = parse(date, "dd/MM/yy", new Date());
      } else {
        // Try generic Date parsing as fallback
        dateObj = new Date(date);
      }
    } else {
      return "";
    }

    if (!isValid(dateObj)) {
      console.warn("Invalid date for professional formatting:", date);
      return "";
    }

    // Format as "E, d MMM, yy" (e.g., "Mon, 16 Jun, 25")
    return format(dateObj, "E, d MMM, yy");
  } catch (error) {
    console.warn("Error formatting date professionally:", error, date);
    return "";
  }
};

/**
 * Convert API date format (YYYY-MM-DD) to professional display format (E, d MMM, yy)
 * @param apiDate - Date string in YYYY-MM-DD format
 * @returns Professional formatted string like "Mon, 16 Jun, 25" or empty string if invalid
 */
export const formatApiDateProfessional = (apiDate: string | null | undefined): string => {
  if (!apiDate) return "";

  try {
    const date = parse(apiDate, "yyyy-MM-dd", new Date());

    if (!isValid(date)) {
      console.warn("Invalid API date format received:", apiDate);
      return "";
    }

    return format(date, "E, d MMM, yy");
  } catch (error) {
    console.warn("Error formatting API date professionally:", error, apiDate);
    return "";
  }
};
