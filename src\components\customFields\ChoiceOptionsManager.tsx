"use client";

import React, { useState } from "react";
import { Box, TextField, Button, IconButton, Typography, Paper, Chip, Menu, MenuItem, Alert, FormControl, InputLabel, Select } from "@mui/material";
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  DragIndicator as Drag<PERSON><PERSON>,
  <PERSON><PERSON> as <PERSON>letteI<PERSON>,
  Sort as SortIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
} from "@mui/icons-material";
import { DragDropContext, Droppable, Draggable } from "@hello-pangea/dnd";
import { UpdateCustomFieldChoiceOptionData, CreateCustomFieldChoiceOptionData, DEFAULT_CHOICE_COLORS } from "@/lib/types/customFields";

type SortMode = "manual" | "alphabetical" | "reverse-alphabetical";

interface ChoiceOptionsManagerProps {
  options: (UpdateCustomFieldChoiceOptionData | CreateCustomFieldChoiceOptionData)[];
  onChange: (options: (UpdateCustomFieldChoiceOptionData | CreateCustomFieldChoiceOptionData)[]) => void;
  disabled?: boolean;
  fieldType?: string; // To determine if default values are supported
}

const ChoiceOptionsManager: React.FC<ChoiceOptionsManagerProps> = ({ options, onChange, disabled = false, fieldType }) => {
  const [newOptionValue, setNewOptionValue] = useState("");
  const [colorMenuAnchor, setColorMenuAnchor] = useState<null | HTMLElement>(null);
  const [selectedOptionIndex, setSelectedOptionIndex] = useState<number | null>(null);
  const [sortMode, setSortMode] = useState<SortMode>("manual");

  // Check if this field type supports default values
  const supportsDefaults = fieldType === "SINGLE_SELECT";

  // Add new option
  const handleAddOption = () => {
    if (!newOptionValue.trim()) return;

    const newOption: UpdateCustomFieldChoiceOptionData | CreateCustomFieldChoiceOptionData = {
      // No ID for new options - backend will create one
      value: newOptionValue.trim(),
      color: DEFAULT_CHOICE_COLORS[options.length % DEFAULT_CHOICE_COLORS.length],
      sort_order: options.length,
      is_default: false, // New options are not default by default
    };

    onChange([...options, newOption]);
    setNewOptionValue("");
  };

  // Remove option
  const handleRemoveOption = (index: number) => {
    const updatedOptions = options.filter((_, i) => i !== index);
    // Update sort orders
    const reorderedOptions = updatedOptions.map((option, i) => ({
      ...option,
      sort_order: i,
    }));
    onChange(reorderedOptions);
  };

  // Update option value
  const handleUpdateOptionValue = (index: number, value: string) => {
    const updatedOptions = options.map((option, i) => (i === index ? { ...option, value } : option));
    onChange(updatedOptions);
  };

  // Open color picker
  const handleOpenColorPicker = (event: React.MouseEvent<HTMLElement>, index: number) => {
    setColorMenuAnchor(event.currentTarget);
    setSelectedOptionIndex(index);
  };

  // Close color picker
  const handleCloseColorPicker = () => {
    setColorMenuAnchor(null);
    setSelectedOptionIndex(null);
  };

  // Update option color
  const handleUpdateOptionColor = (color: string) => {
    if (selectedOptionIndex !== null) {
      const updatedOptions = options.map((option, i) => (i === selectedOptionIndex ? { ...option, color } : option));
      onChange(updatedOptions);
    }
    handleCloseColorPicker();
  };

  // Handle setting/unsetting default value
  const handleSetDefault = (index: number) => {
    if (!supportsDefaults || disabled) return;

    const clickedOption = options[index];
    const isCurrentlyDefault = clickedOption.is_default;

    const updatedOptions = options.map((option, i) => ({
      ...option,
      is_default: isCurrentlyDefault ? false : i === index, // Toggle off if already default, otherwise set as default
    }));
    onChange(updatedOptions);
  };

  // Handle Enter key for adding options
  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === "Enter" && !disabled) {
      event.preventDefault();
      handleAddOption();
    }
  };

  // Handle sort mode change
  const handleSortModeChange = (newSortMode: SortMode) => {
    setSortMode(newSortMode);

    if (newSortMode === "alphabetical" || newSortMode === "reverse-alphabetical") {
      applySorting(newSortMode);
    }
  };

  // Apply sorting to options
  const applySorting = (mode: SortMode) => {
    if (mode === "manual") return;

    const sortedOptions = [...options].sort((a, b) => {
      const comparison = a.value.localeCompare(b.value);
      return mode === "alphabetical" ? comparison : -comparison;
    });

    // Update sort_order to reflect new positions
    const reorderedOptions = sortedOptions.map((option, index) => ({
      ...option,
      sort_order: index,
    }));

    onChange(reorderedOptions);
  };

  // Handle drag and drop
  const handleDragEnd = (result: any) => {
    if (!result.destination || sortMode !== "manual") return;

    const { source, destination } = result;
    if (source.index === destination.index) return;

    const reorderedOptions = Array.from(options);
    const [removed] = reorderedOptions.splice(source.index, 1);
    reorderedOptions.splice(destination.index, 0, removed);

    // Update sort_order to reflect new positions
    const updatedOptions = reorderedOptions.map((option, index) => ({
      ...option,
      sort_order: index,
    }));

    onChange(updatedOptions);
  };

  return (
    <Box>
      {/* Sort Control */}
      {options.length > 1 && (
        <Box mb={2}>
          <FormControl size="small" sx={{ minWidth: 200 }}>
            <InputLabel>Sort</InputLabel>
            <Select
              value={sortMode}
              label="Sort"
              onChange={(e) => handleSortModeChange(e.target.value as SortMode)}
              disabled={disabled}
              startAdornment={<SortIcon sx={{ mr: 1, color: "text.secondary" }} />}
            >
              <MenuItem value="manual">Manual</MenuItem>
              <MenuItem value="alphabetical">Alphabetical (A-Z)</MenuItem>
              <MenuItem value="reverse-alphabetical">Reverse Alphabetical (Z-A)</MenuItem>
            </Select>
          </FormControl>
          {sortMode !== "manual" && (
            <Typography variant="caption" color="text.secondary" sx={{ ml: 2 }}>
              Drag & drop is disabled in {sortMode === "alphabetical" ? "alphabetical" : "reverse alphabetical"} mode
            </Typography>
          )}
        </Box>
      )}

      {/* Existing Options */}
      {options.length > 0 && (
        <Box mb={2}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            Current Options:
          </Typography>

          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="choice-options" isDropDisabled={sortMode !== "manual" || disabled}>
              {(provided, snapshot) => (
                <Box
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                  sx={{
                    display: "flex",
                    flexDirection: "column",
                    gap: 1,
                    backgroundColor: snapshot.isDraggingOver ? "action.hover" : "transparent",
                    borderRadius: 1,
                    p: snapshot.isDraggingOver ? 1 : 0,
                    transition: "all 0.2s ease",
                  }}
                >
                  {options.map((option, index) => {
                    // Use ID if available (existing options), otherwise use value-index (new options)
                    const uniqueKey = option.id || `new-${option.value}-${index}`;
                    return (
                      <Draggable key={uniqueKey} draggableId={uniqueKey} index={index} isDragDisabled={sortMode !== "manual" || disabled}>
                        {(provided, snapshot) => (
                          <Paper
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            variant="outlined"
                            sx={{
                              p: 1.5,
                              display: "flex",
                              alignItems: "center",
                              gap: 1,
                              backgroundColor: snapshot.isDragging ? "action.selected" : "grey.50",
                              transform: snapshot.isDragging ? "rotate(2deg)" : "none",
                              boxShadow: snapshot.isDragging ? 3 : 1,
                              transition: "all 0.2s ease",
                              cursor: sortMode === "manual" && !disabled ? "grab" : "default",
                              "&:hover": {
                                backgroundColor: sortMode === "manual" && !disabled ? "action.hover" : "grey.50",
                              },
                            }}
                          >
                            {/* Drag Handle */}
                            <Box
                              {...provided.dragHandleProps}
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                opacity: sortMode === "manual" && !disabled ? 1 : 0.3,
                              }}
                            >
                              <DragIcon sx={{ color: "text.disabled" }} />
                            </Box>

                            {/* Color Indicator */}
                            <Box
                              sx={{
                                width: 20,
                                height: 20,
                                borderRadius: 1,
                                backgroundColor: option.color,
                                cursor: "pointer",
                                border: "1px solid",
                                borderColor: "grey.300",
                              }}
                              onClick={(e) => !disabled && handleOpenColorPicker(e, index)}
                            />

                            {/* Option Value */}
                            <TextField
                              size="small"
                              value={option.value}
                              onChange={(e) => handleUpdateOptionValue(index, e.target.value)}
                              placeholder="Option name"
                              disabled={disabled}
                              sx={{ flex: 1 }}
                            />

                            {/* Color Picker Button */}
                            <IconButton
                              size="small"
                              onClick={(e) => handleOpenColorPicker(e, index)}
                              disabled={disabled}
                              sx={{ color: "text.secondary" }}
                            >
                              <PaletteIcon fontSize="small" />
                            </IconButton>

                            {/* Set as Default Button - Only for SINGLE_SELECT fields */}
                            {supportsDefaults && (
                              <IconButton
                                size="small"
                                onClick={() => handleSetDefault(index)}
                                disabled={disabled}
                                sx={{
                                  color: option.is_default ? "warning.main" : "text.secondary",
                                  "&:hover": {
                                    color: option.is_default ? "warning.dark" : "warning.main",
                                    backgroundColor: (theme) => theme.palette.warning.main + "10",
                                  },
                                }}
                                title={option.is_default ? "Click to unset as default" : "Set as default"}
                              >
                                {option.is_default ? <StarIcon fontSize="small" /> : <StarBorderIcon fontSize="small" />}
                              </IconButton>
                            )}

                            {/* Delete Button */}
                            <IconButton size="small" onClick={() => handleRemoveOption(index)} disabled={disabled} sx={{ color: "error.main" }}>
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Paper>
                        )}
                      </Draggable>
                    );
                  })}
                  {provided.placeholder}
                </Box>
              )}
            </Droppable>
          </DragDropContext>
        </Box>
      )}

      {/* Add New Option */}
      <Box display="flex" gap={1} alignItems="center">
        <TextField
          size="small"
          label="Add Option"
          value={newOptionValue}
          onChange={(e) => setNewOptionValue(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder="Enter option name"
          disabled={disabled}
          sx={{ flex: 1 }}
        />

        <Button
          variant="outlined"
          startIcon={<AddIcon />}
          onClick={handleAddOption}
          disabled={disabled || !newOptionValue.trim()}
          sx={{ whiteSpace: "nowrap" }}
        >
          Add Option
        </Button>
      </Box>

      {/* Help Text */}
      {options.length === 0 && (
        <Alert severity="info" sx={{ mt: 2 }}>
          Add at least one choice option for this select field. Users will be able to choose from these options.
          {supportsDefaults && " You can set one option as the default for new items."}
        </Alert>
      )}

      {/* Default Value Help Text */}
      {supportsDefaults && options.length > 0 && (
        <Alert severity="info" sx={{ mt: 2 }}>
          <Typography variant="body2">
            <strong>Default Value:</strong> Click the star icon (⭐) next to an option to set it as the default for new items. Click the filled star
            again to remove the default.
            {options.some((opt) => opt.is_default) ? (
              <span>
                {" "}
                Current default: <strong>{options.find((opt) => opt.is_default)?.value}</strong>
              </span>
            ) : (
              <span>
                {" "}
                <em>No default set</em>
              </span>
            )}
          </Typography>
        </Alert>
      )}

      {/* Color Picker Menu */}
      <Menu
        anchorEl={colorMenuAnchor}
        open={Boolean(colorMenuAnchor)}
        onClose={handleCloseColorPicker}
        PaperProps={{
          sx: { p: 1 },
        }}
      >
        <Typography variant="subtitle2" sx={{ px: 1, pb: 1 }}>
          Choose Color
        </Typography>

        <Box display="grid" gridTemplateColumns="repeat(5, 1fr)" gap={0.5}>
          {DEFAULT_CHOICE_COLORS.map((color) => (
            <Box
              key={color}
              sx={{
                width: 32,
                height: 32,
                borderRadius: 1,
                backgroundColor: color,
                cursor: "pointer",
                border: "2px solid",
                borderColor: selectedOptionIndex !== null && options[selectedOptionIndex]?.color === color ? "primary.main" : "transparent",
                "&:hover": {
                  borderColor: "primary.light",
                },
              }}
              onClick={() => handleUpdateOptionColor(color)}
            />
          ))}
        </Box>
      </Menu>
    </Box>
  );
};

export default ChoiceOptionsManager;
