import { NextResponse } from 'next/server'

export interface ApiError {
  message: string
  field?: string
  code?: string
}

export interface ApiErrorResponse {
  error?: string
  message?: string
  errors?: ApiError[]
  [key: string]: any
}

export function createErrorResponse(
  message: string,
  status: number = 400,
  errors?: ApiError[]
): NextResponse<ApiErrorResponse> {
  const response: ApiErrorResponse = {
    message,
  }

  if (errors && errors.length > 0) {
    response.errors = errors
  }

  return NextResponse.json(response, { status })
}

export function createValidationErrorResponse(
  errors: Record<string, string[]>
): NextResponse<ApiErrorResponse> {
  const formattedErrors: ApiError[] = []
  
  Object.entries(errors).forEach(([field, messages]) => {
    messages.forEach(message => {
      formattedErrors.push({ field, message })
    })
  })

  return NextResponse.json({
    message: 'Validation failed',
    errors: formattedErrors
  }, { status: 400 })
}

export function createSuccessResponse<T>(
  data: T,
  status: number = 200
): NextResponse<T> {
  return NextResponse.json(data, { status })
}

export function createUnauthorizedResponse(): NextResponse<ApiErrorResponse> {
  return NextResponse.json({
    message: 'Authentication required'
  }, { status: 401 })
}

export function createForbiddenResponse(): NextResponse<ApiErrorResponse> {
  return NextResponse.json({
    message: 'Access forbidden'
  }, { status: 403 })
}

export function createNotFoundResponse(resource: string = 'Resource'): NextResponse<ApiErrorResponse> {
  return NextResponse.json({
    message: `${resource} not found`
  }, { status: 404 })
}
