import axios from "axios";

const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Function to set the auth token globally for axios requests
export const setAuthToken = (token: string | null) => {
  if (token) {
    apiClient.defaults.headers.common["Authorization"] = `Token ${token}`;
  } else {
    delete apiClient.defaults.headers.common["Authorization"];
  }
};

// Request interceptor for debugging (temporary)
apiClient.interceptors.request.use(
  (config) => {
    // Log all PATCH requests to projects endpoint for debugging 403 issues
    if (config.method === "patch" && config.url?.includes("/workitems/projects/")) {
      console.log("=== AXIOS REQUEST DEBUG ===");
      console.log("URL:", config.url);
      console.log("Method:", config.method);
      console.log("Headers:", config.headers);
      console.log("Data:", config.data);
      console.log("Data type:", typeof config.data);
      console.log("Data JSON:", JSON.stringify(config.data, null, 2));

      // Check if custom_field_inputs exists and is an array
      if (config.data && config.data.custom_field_inputs) {
        console.log("custom_field_inputs:", config.data.custom_field_inputs);
        console.log("custom_field_inputs type:", typeof config.data.custom_field_inputs);
        console.log("custom_field_inputs is array:", Array.isArray(config.data.custom_field_inputs));
        console.log("custom_field_inputs length:", config.data.custom_field_inputs?.length);
      }
      console.log("=== END AXIOS REQUEST DEBUG ===");
    }

    // Log custom fields requests for debugging 403 issues
    if (config.url?.includes("/workitems/custom-fields/")) {
      console.log("=== CUSTOM FIELDS REQUEST DEBUG ===");
      console.log("URL:", config.url);
      console.log("Method:", config.method);
      console.log("Authorization header:", config.headers?.Authorization);
      console.log("Has auth token:", !!config.headers?.Authorization);
      console.log("=== END CUSTOM FIELDS DEBUG ===");
    }

    return config;
  },
  (error) => {
    console.error("Request interceptor error:", error);
    return Promise.reject(error);
  }
);

// Interceptors for global error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle global errors here if needed
    if (error.response?.status === 401) {
      // Token might be expired or invalid
      // You can dispatch a logout action here or redirect to login
      console.warn("Authentication error - token may be invalid");
    }
    return Promise.reject(error);
  }
);

export default apiClient;
