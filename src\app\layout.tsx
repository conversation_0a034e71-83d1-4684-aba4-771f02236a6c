import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/lib/auth/AuthContext";
import { ThemeProvider } from "@/lib/theme/ThemeProvider";
import { NotificationProvider } from "@/components/common/NotificationSnackbar";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import ClientOnly from "@/components/common/ClientOnly";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Agile Life Results System",
  description: "Achieve a balanced and fulfilling life.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`} suppressHydrationWarning>
        <ThemeProvider>
          <NotificationProvider>
            <AuthProvider>
              <div className="flex flex-col min-h-screen">
                <ClientOnly fallback={<div style={{ height: "64px" }} />}>
                  <Navbar />
                </ClientOnly>
                <main className="flex-grow container mx-auto px-4 py-4">{children}</main>
                <Footer />
              </div>
            </AuthProvider>
          </NotificationProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
