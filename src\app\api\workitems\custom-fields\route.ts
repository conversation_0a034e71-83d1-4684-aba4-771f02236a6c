import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/database/prisma";
import { verifyToken, extractTokenFromHeader } from "@/lib/auth/jwt";
import { validateCreateCustomFieldDefinition, ValidationError } from "@/lib/validation/customFields";
import { CreateCustomFieldDefinitionData } from "@/lib/types/customFields";

// Helper function to authenticate request
async function authenticateRequest(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  const token = extractTokenFromHeader(authHeader);

  if (!token) {
    return { error: NextResponse.json({ message: "Authentication required" }, { status: 401 }) };
  }

  const payload = verifyToken(token);
  if (!payload) {
    return { error: NextResponse.json({ message: "Invalid or expired token" }, { status: 401 }) };
  }

  return { userId: payload.userId };
}

// Helper function to create validation error response
function createValidationErrorResponse(errors: ValidationError[]) {
  const errorMap: Record<string, string[]> = {};
  errors.forEach((error) => {
    if (!errorMap[error.field]) errorMap[error.field] = [];
    errorMap[error.field].push(error.message);
  });

  return NextResponse.json(
    {
      message: "Validation failed",
      errors: errorMap,
    },
    { status: 400 }
  );
}

// GET /api/workitems/custom-fields/ - List custom field definitions
export async function GET(request: NextRequest) {
  try {
    console.log("GET /api/workitems/custom-fields/ called");

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Build where clause
    const where: any = {
      user_id: auth.userId,
    };

    // Fetch custom field definitions with choice options
    const definitions = await prisma.workitems_custom_field_definition.findMany({
      where,
      include: {
        choice_options: {
          orderBy: { sort_order: "asc" },
        },
      },
      orderBy: [{ sort_order: "asc" }, { created_at: "asc" }],
    });

    console.log(`Found ${definitions.length} custom field definitions for user ${auth.userId}`);

    return NextResponse.json(definitions);
  } catch (error) {
    console.error("Error fetching custom field definitions:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// POST /api/workitems/custom-fields/ - Create custom field definition
export async function POST(request: NextRequest) {
  try {
    console.log("POST /api/workitems/custom-fields/ called");

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Parse request body
    const body = (await request.json()) as CreateCustomFieldDefinitionData;
    console.log("Request body:", { ...body, choice_options: body.choice_options?.length || 0 });

    // Validate input
    const validationErrors = validateCreateCustomFieldDefinition(body);
    if (validationErrors.length > 0) {
      return createValidationErrorResponse(validationErrors);
    }

    // Check for duplicate name within user
    const existingDefinition = await prisma.workitems_custom_field_definition.findFirst({
      where: {
        user_id: auth.userId,
        name: body.name,
      },
    });

    if (existingDefinition) {
      return NextResponse.json(
        {
          message: "A custom field with this name already exists",
        },
        { status: 400 }
      );
    }

    // Create custom field definition with choice options in a transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create the definition
      const definition = await tx.workitems_custom_field_definition.create({
        data: {
          user_id: auth.userId,
          name: body.name,
          field_type: body.field_type,
          is_required: body.is_required || false,
          sort_order: body.sort_order || 0,
        },
      });

      // Create choice options if provided
      if (body.choice_options && body.choice_options.length > 0) {
        await tx.workitems_custom_field_choice_option.createMany({
          data: body.choice_options.map((option, index) => ({
            field_definition_id: definition.id,
            value: option.value,
            color: option.color,
            sort_order: option.sort_order !== undefined ? option.sort_order : index,
            is_default: option.is_default || false,
          })),
        });
      }

      // Return the complete definition with choice options
      return await tx.workitems_custom_field_definition.findUnique({
        where: { id: definition.id },
        include: {
          choice_options: {
            orderBy: { sort_order: "asc" },
          },
        },
      });
    });

    console.log("Created custom field definition:", result?.id);

    return NextResponse.json(result, { status: 201 });
  } catch (error) {
    console.error("Error creating custom field definition:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
