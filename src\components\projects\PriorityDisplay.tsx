"use client";

import React from "react";
import { Box, Typography, Chip } from "@mui/material";

interface PriorityDisplayProps {
  priorityId?: string;
  priorityName?: string;
  priorityColor?: string;
  onClick: () => void;
  isEditing?: boolean;
}

const PriorityDisplay: React.FC<PriorityDisplayProps> = ({
  priorityId,
  priorityName,
  priorityColor,
  onClick,
  isEditing = false,
}) => {
  // If no priority is set, show placeholder
  if (!priorityId || !priorityName) {
    return (
      <Box
        onClick={onClick}
        sx={{
          display: "flex",
          alignItems: "center",
          gap: 0.5,
          cursor: "pointer",
          padding: "2px 6px",
          borderRadius: "4px",
          "&:hover": {
            backgroundColor: "action.hover",
          },
          opacity: isEditing ? 0.5 : 1,
        }}
      >
        <Typography
          variant="caption"
          sx={{
            color: "text.secondary",
            fontSize: "0.6875rem",
            fontStyle: "italic",
          }}
        >
          [Set Priority]
        </Typography>
      </Box>
    );
  }

  // Show priority with color badge and name
  return (
    <Box
      onClick={onClick}
      sx={{
        display: "flex",
        alignItems: "center",
        gap: 0.5,
        cursor: "pointer",
        padding: "2px 6px",
        borderRadius: "4px",
        "&:hover": {
          backgroundColor: "action.hover",
        },
        opacity: isEditing ? 0.5 : 1,
      }}
    >
      {/* Color badge */}
      <Box
        sx={{
          width: 8,
          height: 8,
          borderRadius: "50%",
          backgroundColor: priorityColor || "#757575",
          flexShrink: 0,
        }}
      />
      {/* Priority name */}
      <Typography
        variant="caption"
        sx={{
          color: "text.secondary",
          fontSize: "0.6875rem",
          fontWeight: 400,
          lineHeight: 1.3,
        }}
      >
        {priorityName}
      </Typography>
    </Box>
  );
};

export default PriorityDisplay;
