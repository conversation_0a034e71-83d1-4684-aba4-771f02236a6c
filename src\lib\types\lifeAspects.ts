/**
 * Life Aspects Types
 * Based on API documentation for /api/workitems/life-aspects/
 */

export interface LifeAspect {
  id: string; // UUID from backend
  name: string;
  description?: string;
  color?: string; // Hex color code
  sort_order?: number; // Frontend field name
  display_order?: number; // Backend field name
  created_at: string;
  updated_at: string;
  user: string; // User UUID from backend
}

export interface CreateLifeAspectData {
  name: string;
  description?: string;
  color: string; // Required hex color code
  sort_order?: number;
}

export interface UpdateLifeAspectData {
  name?: string;
  description?: string;
  color?: string; // Hex color code
  sort_order?: number;
}

export interface LifeAspectError {
  message: string;
  field?: string;
  details?: Record<string, string[]>;
}

// Paginated response interface
export interface PaginatedLifeAspectsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: LifeAspect[];
}

// Default Life Aspects as per API documentation
export const DEFAULT_LIFE_ASPECTS = [
  { name: "Mind", description: "Mental development and learning" },
  { name: "Body", description: "Physical health and fitness" },
  { name: "Emotions", description: "Emotional intelligence and well-being" },
  { name: "Career", description: "Professional development" },
  { name: "Finance", description: "Financial planning and wealth building" },
  { name: "Relationships", description: "Personal and social connections" },
  { name: "Fun & Recreation", description: "Leisure and hobbies" },
  { name: "Spiritual", description: "Spiritual growth and purpose" },
  { name: "Contribution & Legacy", description: "Giving back and impact" },
] as const;
