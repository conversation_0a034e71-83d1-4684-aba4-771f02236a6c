/**
 * Custom Fields Types
 * Based on CUSTOM_FIELDS_IMPLEMENTATION_SUMMARY.md and USING_CUSTOM_APIS.md
 */

// Field types supported by the system
export type CustomFieldType =
  | "TEXT"
  | "TEXTAREA"
  | "NUMBER"
  | "BOOLEAN"
  | "DATE"
  | "DATETIME"
  | "WEEK"
  | "EMAIL"
  | "URL"
  | "PHONE"
  | "SINGLE_SELECT"
  | "MULTI_SELECT";

// Choice option for SELECT field types
export interface CustomFieldChoiceOption {
  id: string; // UUID
  value: string; // Display name
  color: string; // Hex color code
  sort_order: number;
  is_default: boolean; // Whether this option is the default for new work items
  created_at: string;
  updated_at: string;
}

// Custom field definition
export interface CustomFieldDefinition {
  id: string; // UUID
  name: string;
  field_type: CustomFieldType;
  is_required: boolean;
  sort_order: number;
  choice_options: CustomFieldChoiceOption[];
  created_at: string;
  updated_at: string;
  user: string; // User UUID
}

// Create custom field definition data
export interface CreateCustomFieldDefinitionData {
  name: string;
  field_type: CustomFieldType;
  is_required?: boolean;
  sort_order?: number;
  choice_options?: CreateCustomFieldChoiceOptionData[];
}

// Update custom field definition data
// According to USING_CUSTOM_APIS.md, PUT requests require ALL required fields
// Updated to support Smart Matching Logic for choice options
export interface UpdateCustomFieldDefinitionData {
  name: string;
  field_type: CustomFieldType;
  is_required: boolean;
  sort_order: number;
  choice_options?: UpdateCustomFieldChoiceOptionData[];
}

// Create choice option data (for new options without ID)
export interface CreateCustomFieldChoiceOptionData {
  value: string;
  color: string;
  sort_order?: number;
  is_default?: boolean; // Whether this option should be the default
}

// Update choice option data (for existing options with ID or new options without ID)
// This supports the backend's Smart Matching Logic
export interface UpdateCustomFieldChoiceOptionData {
  id?: string; // Present for existing options, absent for new options
  value: string;
  color: string;
  sort_order: number;
  is_default?: boolean; // Whether this option should be the default
}

// Custom field input for assigning values to projects/outcomes
export interface CustomFieldInput {
  definition_id: string; // UUID of the custom field definition
  value: any; // Value depends on field type
}

// Resolved custom field (from API responses)
export interface ResolvedCustomField {
  definition_id: string;
  definition_name: string;
  field_type: CustomFieldType;
  is_required: boolean;
  sort_order: number;
  value: any;
  display_value: string;
  choice_option?: CustomFieldChoiceOption; // For SELECT fields
  choice_options?: CustomFieldChoiceOption[]; // For MULTI_SELECT fields
}

// API response types
export interface CustomFieldDefinitionResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: CustomFieldDefinition[];
}

// Error types
export interface CustomFieldError {
  message: string;
  field?: string;
  code?: string;
}

// Field type display information
export interface FieldTypeInfo {
  type: CustomFieldType;
  label: string;
  description: string;
  icon: string; // Material Icon name
  hasChoices: boolean;
}

// Validation result
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// Drag and drop types
export interface DragDropResult {
  sourceIndex: number;
  destinationIndex: number;
  sourceId: string;
  destinationId?: string;
}

// Form state types
export interface CustomFieldFormData {
  name: string;
  field_type: CustomFieldType;
  is_required: boolean;
  choice_options: CreateCustomFieldChoiceOptionData[];
}

export interface ChoiceOptionFormData {
  value: string;
  color: string;
}

// UI state types
export interface CustomFieldsPageState {
  definitions: CustomFieldDefinition[];
  loading: boolean;
  error: string | null;
  selectedDefinition: CustomFieldDefinition | null;
  isCreateModalOpen: boolean;
  isEditModalOpen: boolean;
  isDeleteDialogOpen: boolean;
  sortBy: "name" | "created_at" | "sort_order";
  showEmptyGroups: boolean;
}

// Constants for field types
export const FIELD_TYPES: FieldTypeInfo[] = [
  { type: "TEXT", label: "Text Input", description: "Single line text", icon: "text_fields", hasChoices: false },
  { type: "TEXTAREA", label: "Text Area", description: "Multi-line text", icon: "notes", hasChoices: false },
  { type: "NUMBER", label: "Number", description: "Numeric values", icon: "numbers", hasChoices: false },
  { type: "BOOLEAN", label: "Checkbox", description: "True/false toggle", icon: "check_box", hasChoices: false },
  { type: "DATE", label: "Date Picker", description: "Date selection", icon: "calendar_today", hasChoices: false },
  { type: "DATETIME", label: "Date & Time", description: "Date and time selection", icon: "schedule", hasChoices: false },
  { type: "WEEK", label: "Week Picker", description: "Week selection", icon: "date_range", hasChoices: false },
  { type: "EMAIL", label: "Email", description: "Email address", icon: "email", hasChoices: false },
  { type: "URL", label: "URL", description: "Web address", icon: "link", hasChoices: false },
  { type: "PHONE", label: "Phone", description: "Phone number", icon: "phone", hasChoices: false },
  { type: "SINGLE_SELECT", label: "Dropdown", description: "Single choice selection", icon: "arrow_drop_down", hasChoices: true },
  { type: "MULTI_SELECT", label: "Multi-Select", description: "Multiple choice selection", icon: "checklist", hasChoices: true },
];

// Default colors for choice options
export const DEFAULT_CHOICE_COLORS = [
  "#1976d2", // Blue
  "#388e3c", // Green
  "#f57c00", // Orange
  "#d32f2f", // Red
  "#7b1fa2", // Purple
  "#00796b", // Teal
  "#5d4037", // Brown
  "#616161", // Grey
  "#e91e63", // Pink
  "#ff5722", // Deep Orange
];
