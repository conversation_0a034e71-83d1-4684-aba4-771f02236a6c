"use client";

import React from "react";
import { Menu, MenuItem, ListItemIcon, ListItemText, Divider } from "@mui/material";
import { Add as AddIcon, Visibility as ViewIcon, Delete as DeleteIcon, Edit as EditIcon } from "@mui/icons-material";

interface ProjectActionsMenuProps {
  projectId: string;
  projectName: string;
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
}

const ProjectActionsMenu: React.FC<ProjectActionsMenuProps> = ({ projectId, projectName, anchorEl, open, onClose }) => {
  const handleEditDetails = () => {
    console.log("Edit Details for project:", projectId, projectName);
    // TODO: Implement edit details functionality (open comprehensive edit modal)
    onClose();
  };

  const handleAddSubProject = () => {
    console.log("Add Sub-Project for project:", projectId, projectName);
    // TODO: Implement add sub-project functionality
    onClose();
  };

  const handleViewOutcomes = () => {
    console.log("View Outcomes for project:", projectId, projectName);
    // TODO: Implement view outcomes functionality
    onClose();
  };

  const handleDeleteProject = () => {
    console.log("Delete Project:", projectId, projectName);
    // TODO: Implement delete project functionality with confirmation
    onClose();
  };

  return (
    <Menu
      anchorEl={anchorEl}
      open={open}
      onClose={onClose}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "right",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "right",
      }}
      PaperProps={{
        sx: {
          minWidth: 200,
          boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.15)",
          borderRadius: 2,
        },
      }}
    >
      <MenuItem onClick={handleEditDetails}>
        <ListItemIcon>
          <EditIcon fontSize="small" />
        </ListItemIcon>
        <ListItemText primary="Edit Details..." />
      </MenuItem>

      <Divider />

      <MenuItem onClick={handleAddSubProject}>
        <ListItemIcon>
          <AddIcon fontSize="small" />
        </ListItemIcon>
        <ListItemText primary="Add Sub-Project" />
      </MenuItem>

      <MenuItem onClick={handleViewOutcomes}>
        <ListItemIcon>
          <ViewIcon fontSize="small" />
        </ListItemIcon>
        <ListItemText primary="View Outcomes" />
      </MenuItem>

      <Divider />

      <MenuItem
        onClick={handleDeleteProject}
        sx={{
          color: "error.main",
          "&:hover": {
            backgroundColor: "error.light",
            color: "error.contrastText",
          },
        }}
      >
        <ListItemIcon sx={{ color: "inherit" }}>
          <DeleteIcon fontSize="small" />
        </ListItemIcon>
        <ListItemText primary="Delete Project" />
      </MenuItem>
    </Menu>
  );
};

export default ProjectActionsMenu;
