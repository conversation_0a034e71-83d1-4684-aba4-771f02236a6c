# Agile Life Results System - Design System & Style Guide

## Overview

This document defines the comprehensive design system for the Agile Life Results System frontend application. Our design philosophy combines **Material Design 3 principles** with a **minimalistic, modern aesthetic** focused on **productivity and user focus**.

## Design Philosophy

### Core Principles

1. **Minimalism**: Clean, uncluttered interfaces with ample whitespace
2. **Modern & Sleek**: Contemporary look with clean lines and purposeful interactions
3. **Productivity-Focused**: Design choices that enhance user focus and efficiency
4. **Material Design Inspired**: Leveraging proven usability principles while maintaining minimalism

### Implementation Strategy

We use **Material UI (MUI)** as our component library, extensively themed to achieve our minimalistic aesthetic while retaining Material Design's interaction patterns and accessibility features.

## Color Palette

### Primary Colors

- **Primary Main**: `#1976d2` (Material Blue 700) - Used for primary actions, links, and brand elements
- **Primary Light**: `#42a5f5` (Material Blue 400) - Used for hover states and lighter accents
- **Primary Dark**: `#1565c0` (Material Blue 800) - Used for pressed states and emphasis

### Secondary Colors

- **Secondary Main**: `#757575` (Material Grey 600) - Used for secondary actions and subtle elements
- **Secondary Light**: `#9e9e9e` (Material Grey 500) - Used for lighter secondary elements
- **Secondary Dark**: `#424242` (Material Grey 800) - Used for secondary text and emphasis

### Semantic Colors

- **Success**: `#2e7d32` (Material Green 700) - Success states, positive feedback
- **Warning**: `#ed6c02` (Material Orange 700) - Warning states, caution
- **Error**: `#d32f2f` (Material Red 700) - Error states, destructive actions
- **Info**: `#0288d1` (Material Light Blue 700) - Informational content

### Neutral/Surface Colors

- **Background Default**: `#fafafa` - Page background (very light grey)
- **Background Paper**: `#ffffff` - Card and surface backgrounds (white)
- **Text Primary**: `#424242` (Grey 800) - Primary text color
- **Text Secondary**: `#757575` (Grey 600) - Secondary text color
- **Divider**: `#e0e0e0` (Grey 300) - Borders and dividers

### Usage Guidelines

- Use primary colors sparingly for maximum impact
- Maintain high contrast ratios (WCAG AA compliance)
- Prefer neutral colors for large surfaces
- Use semantic colors consistently for their intended purposes

## Typography

### Font Family

**Primary**: Inter (Google Fonts)
**Fallback**: 'Roboto', 'Helvetica', 'Arial', sans-serif

### Type Scale (Material Design 3 Inspired)

#### Display Styles

- **H1 (Display Large)**: 56px, Weight 400, Line Height 1.2
- **H2 (Display Medium)**: 44px, Weight 400, Line Height 1.2
- **H3 (Display Small)**: 36px, Weight 400, Line Height 1.3

#### Headline Styles

- **H4 (Headline Large)**: 32px, Weight 500, Line Height 1.3
- **H5 (Headline Medium)**: 24px, Weight 500, Line Height 1.4
- **H6 (Headline Small)**: 20px, Weight 500, Line Height 1.4

#### Body & UI Styles

- **Body 1 (Body Large)**: 16px, Weight 400, Line Height 1.6
- **Body 2 (Body Medium)**: 14px, Weight 400, Line Height 1.6
- **Caption (Body Small)**: 12px, Weight 400, Line Height 1.5
- **Button**: 14px, Weight 500, Line Height 1.4, No text transform

### Typography Guidelines

- Use sentence case instead of ALL CAPS for better readability
- Maintain consistent line heights for vertical rhythm
- **Current font weights**: 400 (regular) and 500 (medium) for minimalism
- **Future expansion**: If additional hierarchy is needed, consider 600 (semibold) for data emphasis or 300 (light) for large display text, but use sparingly to maintain clean aesthetic
- Always test new weights against the minimalistic principle

## Layout & Spacing

### Spacing System (8dp Grid)

Based on Material Design's 8-density-pixel grid system:

- **XS**: 4px (0.5 units)
- **SM**: 8px (1 unit)
- **MD**: 16px (2 units)
- **LG**: 24px (3 units)
- **XL**: 32px (4 units)
- **2XL**: 48px (6 units)
- **3XL**: 64px (8 units)

### Container & Breakpoints

- **Max Width**: 1200px (lg)
- **Responsive Breakpoints**: Follow Material UI defaults
  - xs: 0px
  - sm: 600px
  - md: 900px
  - lg: 1200px
  - xl: 1536px

### Layout Principles

- Use consistent spacing multiples of 8px
- Maintain clear visual hierarchy through spacing
- Ensure adequate whitespace for readability
- Follow responsive design patterns

## Component Styling

### Buttons

**Material UI Button component with custom theming**

#### Variants & Usage

- **Contained (Primary)**: Main call-to-action buttons (e.g., "Create Account", "Save", "Submit")
  - Use sparingly - only one primary action per section
  - Color: Primary blue (#1976d2)
- **Outlined (Secondary)**: Secondary actions that need emphasis (e.g., "Learn More", "Cancel")
  - Use for important but non-primary actions
  - Color: Primary blue border with transparent background
- **Text (Tertiary)**: Low-emphasis actions (e.g., "Skip", "Back", navigation links)
  - Use for less important actions
  - Minimal visual weight

#### Styling Overrides

- Border radius: 8px (slightly rounded)
- No text transform (sentence case)
- No box shadow by default
- Subtle hover effects with minimal elevation
- Consistent padding: Small (8px 16px), Medium (12px 24px), Large (16px 32px)

### Cards

**Material UI Card component with custom theming**

#### Styling Features

- Border radius: 12px for modern look
- Subtle shadow: `0px 1px 3px rgba(0, 0, 0, 0.08)`
- Hover effect: Slightly increased shadow and subtle lift
- Smooth transitions (0.2s ease-in-out)

#### Interactive States

- **Default**: Minimal shadow for clean appearance
- **Hover**: `transform: translateY(-2px)` + increased shadow for feedback
- **Focus**: Clear focus ring for keyboard navigation
- **Active**: Brief scale effect (0.98) for tactile feedback
- **Animation Duration**: 200ms for responsiveness without distraction

### Navigation

**Material UI AppBar with minimalistic styling**

#### Features

- Clean white background with subtle border
- No heavy shadows
- Responsive design with mobile considerations
- Clear visual hierarchy

## Interaction Patterns

### Hover Effects

- Subtle elevation changes for cards
- Color transitions for buttons
- Transform effects (translateY) for interactive elements

### Focus States

- Clear focus indicators for accessibility
- Consistent focus ring styling
- Keyboard navigation support

### Loading States

- Material UI CircularProgress component
- Consistent sizing and positioning
- Clear loading messages

## Accessibility

### Color Contrast

- **Primary text on white background**: #424242 on #ffffff = 9.74:1 (WCAG AAA ✓)
- **Secondary text on white background**: #757575 on #ffffff = 4.54:1 (WCAG AA ✓)
- **Primary button text**: #ffffff on #1976d2 = 5.74:1 (WCAG AA ✓)
- **Navbar text**: #424242 on #ffffff = 9.74:1 (WCAG AAA ✓)
- All interactive elements exceed WCAG AA standards (4.5:1 ratio minimum)
- Color is not the only means of conveying information

### Typography

- Readable font sizes (minimum 14px for body text)
- Clear visual hierarchy
- Adequate line spacing for readability

### Interactive Elements

- Minimum touch target size of 44px
- Clear focus indicators
- Keyboard navigation support

## Implementation Guidelines

### Component Usage

1. **Always use Material UI components** as the foundation
2. **Apply custom theming** through the theme configuration
3. **Maintain consistency** across all components
4. **Follow Material Design patterns** for interactions

### Code Standards

- Use the `sx` prop for component-specific styling
- Leverage theme tokens for colors and spacing
- Maintain responsive design patterns
- Follow TypeScript best practices

### Technical Stack

- **Framework**: Next.js 15.3.2 with App Router (stable production version)
- **UI Library**: Material UI (MUI) v7 with Emotion
- **Styling**: Hybrid approach - Material UI theme system + Tailwind CSS v4 utilities
- **Icons**: Material Icons for consistency
- **Typography**: Inter font family from Google Fonts
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT tokens with bcrypt

### File Organization

- Theme configuration: `src/lib/theme/`
- Component overrides in theme file
- Consistent import patterns

## Future Considerations

### Scalability

- Design system can accommodate new components
- Theme structure supports easy updates
- Consistent patterns for team development

### Maintenance

- Regular review of design decisions
- Updates to align with Material Design evolution
- Performance optimization of theme configuration

---

**Note**: This style guide serves as the single source of truth for all UI development. All new components and features must adhere to these design principles and implementation guidelines.
