"use client";

import React, { useState, useEffect } from "react";
import {
  Container,
  Typo<PERSON>,
  Box,
  Card,
  CardContent,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormControl,
  FormLabel,
  Alert,
  CircularProgress,
  Snackbar,
  Switch,
} from "@mui/material";
import { Settings as SettingsIcon } from "@mui/icons-material";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import { userPreferencesService } from "@/lib/api/userPreferencesService";
import { WeekStartDay } from "@/lib/utils/weekUtils";

const PreferencesPage = () => {
  // State management
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [weekStartsOn, setWeekStartsOn] = useState<WeekStartDay>("Sunday");
  const [enableInheritance, setEnableInheritance] = useState(true);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");

  // Load user preferences on mount
  useEffect(() => {
    loadUserPreferences();
  }, []);

  const loadUserPreferences = async () => {
    try {
      setLoading(true);
      setError(null);
      const preferences = await userPreferencesService.getPreferences();
      setWeekStartsOn(preferences.week_starts_on || "Sunday");
      setEnableInheritance(preferences.enable_inheritance ?? true);
    } catch (err: any) {
      console.error("Error loading user preferences:", err);
      setError("Failed to load preferences. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleWeekStartChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const newWeekStart = event.target.value as WeekStartDay;

    try {
      setSaving(true);
      setError(null);

      // Update the preference via API using the dedicated method
      await userPreferencesService.updateWeekStartsOn(newWeekStart);

      // Update local state
      setWeekStartsOn(newWeekStart);

      // Show success message
      setSnackbarMessage("Week start preference updated successfully!");
      setSnackbarOpen(true);
    } catch (err: any) {
      console.error("Error updating week start preference:", err);
      setError("Failed to update preference. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const handleInheritanceChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const newInheritanceEnabled = event.target.checked;

    try {
      setSaving(true);
      setError(null);

      // Update the preference via API using the dedicated method
      await userPreferencesService.updateInheritanceEnabled(newInheritanceEnabled);

      // Update local state
      setEnableInheritance(newInheritanceEnabled);

      // Show success message
      setSnackbarMessage("Property inheritance preference updated successfully!");
      setSnackbarOpen(true);
    } catch (err: any) {
      console.error("Error updating inheritance preference:", err);
      setError("Failed to update preference. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  return (
    <ProtectedRoute>
      <Container maxWidth="md" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box display="flex" alignItems="center" gap={2} mb={4}>
          <SettingsIcon color="primary" />
          <Typography variant="h4" component="h1" sx={{ fontWeight: 500 }}>
            Preferences
          </Typography>
        </Box>

        <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
          Customize your application settings and preferences.
        </Typography>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {/* Loading State */}
        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" py={8}>
            <CircularProgress />
          </Box>
        ) : (
          <Box display="grid" gap={3}>
            {/* Week Settings Card */}
            <Card
              sx={{
                borderRadius: "12px",
                boxShadow: "0px 1px 3px rgba(0, 0, 0, 0.08)",
                transition: "all 0.2s ease-in-out",
                "&:hover": {
                  boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.15)",
                },
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" component="h2" sx={{ fontWeight: 500, mb: 2 }}>
                  Week Settings
                </Typography>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Choose which day your week starts on. This affects calendar displays and week calculations throughout the application.
                </Typography>

                <FormControl component="fieldset" disabled={saving}>
                  <FormLabel component="legend" sx={{ mb: 2, fontWeight: 500 }}>
                    Week Starts On
                  </FormLabel>
                  <RadioGroup value={weekStartsOn} onChange={handleWeekStartChange} sx={{ gap: 1 }}>
                    <FormControlLabel
                      value="Sunday"
                      control={<Radio />}
                      label={
                        <Box>
                          <Typography variant="body1" fontWeight={500}>
                            Sunday
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Traditional week start (Sunday - Saturday)
                          </Typography>
                        </Box>
                      }
                      sx={{
                        border: "1px solid",
                        borderColor: weekStartsOn === "Sunday" ? "primary.main" : "divider",
                        borderRadius: 2,
                        p: 2,
                        m: 0,
                        transition: "all 0.2s ease-in-out",
                        "&:hover": {
                          borderColor: "primary.main",
                          backgroundColor: "action.hover",
                        },
                      }}
                    />
                    <FormControlLabel
                      value="Monday"
                      control={<Radio />}
                      label={
                        <Box>
                          <Typography variant="body1" fontWeight={500}>
                            Monday
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            ISO standard week start (Monday - Sunday)
                          </Typography>
                        </Box>
                      }
                      sx={{
                        border: "1px solid",
                        borderColor: weekStartsOn === "Monday" ? "primary.main" : "divider",
                        borderRadius: 2,
                        p: 2,
                        m: 0,
                        transition: "all 0.2s ease-in-out",
                        "&:hover": {
                          borderColor: "primary.main",
                          backgroundColor: "action.hover",
                        },
                      }}
                    />
                  </RadioGroup>
                </FormControl>

                {saving && (
                  <Box display="flex" alignItems="center" gap={2} mt={2}>
                    <CircularProgress size={16} />
                    <Typography variant="body2" color="text.secondary">
                      Saving preference...
                    </Typography>
                  </Box>
                )}
              </CardContent>
            </Card>

            {/* Project & Outcome Settings Card */}
            <Card
              sx={{
                borderRadius: "12px",
                boxShadow: "0px 1px 3px rgba(0, 0, 0, 0.08)",
                transition: "all 0.2s ease-in-out",
                "&:hover": {
                  boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.15)",
                },
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Typography variant="h6" component="h2" sx={{ fontWeight: 500, mb: 2 }}>
                  Project & Outcome Settings
                </Typography>

                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Configure how new sub-projects and outcomes inherit properties from their parent projects.
                </Typography>

                <FormControl component="fieldset" disabled={saving}>
                  <FormControlLabel
                    control={<Switch checked={enableInheritance} onChange={handleInheritanceChange} color="primary" />}
                    label={
                      <Box>
                        <Typography variant="body1" fontWeight={500}>
                          Enable Property Inheritance
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          When enabled, new sub-projects and outcomes will automatically inherit values from their parent project.
                        </Typography>
                      </Box>
                    }
                    sx={{
                      alignItems: "flex-start",
                      "& .MuiFormControlLabel-label": {
                        ml: 1,
                      },
                    }}
                  />
                </FormControl>

                {saving && (
                  <Box display="flex" alignItems="center" gap={2} mt={2}>
                    <CircularProgress size={16} />
                    <Typography variant="body2" color="text.secondary">
                      Saving preference...
                    </Typography>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Box>
        )}

        {/* Success Snackbar */}
        <Snackbar
          open={snackbarOpen}
          autoHideDuration={4000}
          onClose={handleSnackbarClose}
          anchorOrigin={{ vertical: "bottom", horizontal: "center" }}
        >
          <Alert onClose={handleSnackbarClose} severity="success" sx={{ width: "100%" }}>
            {snackbarMessage}
          </Alert>
        </Snackbar>
      </Container>
    </ProtectedRoute>
  );
};

export default PreferencesPage;
