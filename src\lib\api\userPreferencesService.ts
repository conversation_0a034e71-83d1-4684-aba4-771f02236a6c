import apiClient from "./apiClient";
import { API_ENDPOINTS } from "@/lib/utils/constants";
import { UserPreferences, UserPreferencesData, UpdateUserPreferencesRequest, UserPreferencesError } from "@/lib/types/userPreferences";
import { AxiosError } from "axios";

/**
 * Parse API errors for user preferences operations
 */
const parsePreferencesError = (error: AxiosError): UserPreferencesError => {
  if (error.response?.data) {
    const data = error.response.data as any;

    // Handle field-specific validation errors
    if (data.preferences_data) {
      const fieldErrors = data.preferences_data;
      if (fieldErrors.project_view_fields) {
        return {
          message: "Invalid project view field preferences",
          field: "project_view_fields",
          code: "validation_error",
        };
      }
    }

    // Handle simple error messages
    if (data.message || data.detail || data.error) {
      return { message: data.message || data.detail || data.error };
    }
  }

  // Default error message
  return {
    message: error.response?.status === 404 ? "Preferences not found" : "An unexpected error occurred while managing preferences.",
  };
};

export const userPreferencesService = {
  /**
   * Get user preferences - creates default preferences if none exist
   */
  async getPreferences(): Promise<UserPreferences> {
    try {
      const response = await apiClient.get<UserPreferences>(API_ENDPOINTS.USERS.PREFERENCES);
      return response.data;
    } catch (error) {
      throw parsePreferencesError(error as AxiosError);
    }
  },

  /**
   * Update user preferences (full replace)
   */
  async updatePreferences(preferencesData: UserPreferencesData): Promise<UserPreferences> {
    try {
      const payload = {
        preferences: preferencesData,
      };

      const response = await apiClient.put<UserPreferences>(API_ENDPOINTS.USERS.PREFERENCES, payload);
      return response.data;
    } catch (error) {
      throw parsePreferencesError(error as AxiosError);
    }
  },

  /**
   * Partially update user preferences
   */
  async patchPreferences(preferencesData: Partial<UserPreferencesData>): Promise<UserPreferences> {
    try {
      const payload = {
        preferences: preferencesData,
      };

      const response = await apiClient.patch<UserPreferences>(API_ENDPOINTS.USERS.PREFERENCES, payload);
      return response.data;
    } catch (error) {
      throw parsePreferencesError(error as AxiosError);
    }
  },

  /**
   * Update week starts on preference
   */
  async updateWeekStartsOn(weekStartsOn: "Sunday" | "Monday"): Promise<UserPreferences> {
    try {
      const payload = {
        week_starts_on: weekStartsOn,
      };

      const response = await apiClient.patch<UserPreferences>(API_ENDPOINTS.USERS.PREFERENCES, payload);
      return response.data;
    } catch (error) {
      throw parsePreferencesError(error as AxiosError);
    }
  },

  /**
   * Update inheritance preference
   */
  async updateInheritanceEnabled(enableInheritance: boolean): Promise<UserPreferences> {
    try {
      const payload = {
        enable_inheritance: enableInheritance,
      };

      const response = await apiClient.patch<UserPreferences>(API_ENDPOINTS.USERS.PREFERENCES, payload);
      return response.data;
    } catch (error) {
      throw parsePreferencesError(error as AxiosError);
    }
  },

  /**
   * Pin a custom field in project view
   */
  async pinCustomField(fieldId: string | null): Promise<UserPreferences> {
    try {
      const preferences = await this.getPreferences();
      const updatedPreferences: UserPreferencesData = {
        ...preferences.preferences_data,
        project_view_fields: {
          ...preferences.preferences_data.project_view_fields,
          pinned_field_id: fieldId,
        },
      };

      return await this.updatePreferences(updatedPreferences);
    } catch (error) {
      throw parsePreferencesError(error as AxiosError);
    }
  },

  /**
   * Hide/show custom fields in project view
   */
  async updateHiddenFields(hiddenFieldIds: string[]): Promise<UserPreferences> {
    try {
      const preferences = await this.getPreferences();
      const updatedPreferences: UserPreferencesData = {
        ...preferences.preferences_data,
        project_view_fields: {
          ...preferences.preferences_data.project_view_fields,
          hidden_field_ids: hiddenFieldIds,
        },
      };

      return await this.updatePreferences(updatedPreferences);
    } catch (error) {
      throw parsePreferencesError(error as AxiosError);
    }
  },

  /**
   * Toggle visibility of a custom field
   */
  async toggleCustomFieldVisibility(fieldId: string): Promise<UserPreferences> {
    try {
      const preferences = await this.getPreferences();
      const hiddenFields = preferences.preferences_data.project_view_fields?.hidden_field_ids || [];

      let updatedHiddenFields: string[];
      if (hiddenFields.includes(fieldId)) {
        // Field is hidden, show it
        updatedHiddenFields = hiddenFields.filter((id) => id !== fieldId);
      } else {
        // Field is visible, hide it
        updatedHiddenFields = [...hiddenFields, fieldId];
      }

      return await this.updateHiddenFields(updatedHiddenFields);
    } catch (error) {
      throw parsePreferencesError(error as AxiosError);
    }
  },
};
