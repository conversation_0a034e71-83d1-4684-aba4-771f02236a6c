"use client";

import React, { useState } from "react";
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Button,
  Alert,
} from "@mui/material";
import WeekPicker from "@/components/forms/WeekPicker";
import { formatWeekForDisplay, getCurrentWeek } from "@/lib/utils/weekUtils";

const TestWeekPickerPage: React.FC = () => {
  const [selectedWeek, setSelectedWeek] = useState<string | null>(null);
  const [weekStartsOn, setWeekStartsOn] = useState<"Sunday" | "Monday">("Sunday");

  const handleWeekChange = (week: string | null) => {
    setSelectedWeek(week);
  };

  const handleSetCurrentWeek = () => {
    const currentWeek = getCurrentWeek(weekStartsOn);
    setSelectedWeek(currentWeek);
  };

  const handleClear = () => {
    setSelectedWeek(null);
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Week Picker Component Test
      </Typography>
      
      <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
        This page demonstrates the WeekPicker component functionality. The component respects the user's week start preference and provides a calendar-based week selection interface.
      </Typography>

      <Grid container spacing={3}>
        {/* Week Picker Demo */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Week Picker Demo
            </Typography>
            
            <Box sx={{ mb: 3 }}>
              <WeekPicker
                label="Select a Week"
                value={selectedWeek}
                onChange={handleWeekChange}
                required
                fullWidth
                size="small"
              />
            </Box>

            <Box sx={{ display: "flex", gap: 1, mb: 2 }}>
              <Button 
                variant="outlined" 
                size="small" 
                onClick={handleSetCurrentWeek}
              >
                Set Current Week
              </Button>
              <Button 
                variant="outlined" 
                size="small" 
                onClick={handleClear}
              >
                Clear
              </Button>
            </Box>

            {selectedWeek && (
              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>Selected Week:</strong> {selectedWeek}
                </Typography>
                <Typography variant="body2">
                  <strong>Display Format:</strong> {formatWeekForDisplay(selectedWeek, weekStartsOn)}
                </Typography>
              </Alert>
            )}
          </Paper>
        </Grid>

        {/* Information Panel */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Component Features
            </Typography>
            
            <Box component="ul" sx={{ pl: 2 }}>
              <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                📅 Calendar-based week selection interface
              </Typography>
              <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                🎯 Hover effect highlights entire week
              </Typography>
              <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                ⚙️ Respects user's week start preference (Sunday/Monday)
              </Typography>
              <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                📱 Responsive design with Material UI theming
              </Typography>
              <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                🔄 Month navigation with arrow buttons
              </Typography>
              <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                ✨ Smooth animations and transitions
              </Typography>
              <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                📊 YYYY-WNN format storage (e.g., 2025-W25)
              </Typography>
              <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                👁️ User-friendly display format (e.g., W25: 15 Jun - 21 Jun)
              </Typography>
            </Box>

            <Typography variant="h6" sx={{ mt: 3, mb: 2 }}>
              Technical Details
            </Typography>
            
            <Box component="ul" sx={{ pl: 2 }}>
              <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                <strong>Storage Format:</strong> YYYY-WNN (ISO week format)
              </Typography>
              <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                <strong>Display Format:</strong> W{"{week}"}: {"{start}"} - {"{end}"}
              </Typography>
              <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                <strong>Week Calculation:</strong> Uses date-fns library
              </Typography>
              <Typography component="li" variant="body2" sx={{ mb: 1 }}>
                <strong>User Preferences:</strong> Fetches week_starts_on from API
              </Typography>
            </Box>
          </Paper>
        </Grid>

        {/* Usage Example */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Usage in Custom Fields
            </Typography>
            
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              When you create a custom field with type "WEEK", this component will automatically be used in forms:
            </Typography>

            <Box sx={{ bgcolor: "grey.100", p: 2, borderRadius: 1, fontFamily: "monospace" }}>
              <Typography variant="body2" component="pre">
{`// In CustomFieldsForm.tsx
case "WEEK":
  return (
    <WeekPicker
      label={definition.name}
      value={currentValue || null}
      onChange={(value) => handleFieldChange(definition.id, value)}
      disabled={disabled}
      required={definition.is_required}
      fullWidth
      size="small"
    />
  );`}
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default TestWeekPickerPage;
