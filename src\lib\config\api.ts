// API Configuration for Backend Migration
// This file helps manage the transition from Django to Next.js API

// Environment variables for API configuration
const NEXT_PUBLIC_API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000/api";
const USE_NEXTJS_BACKEND = process.env.NEXT_PUBLIC_USE_NEXTJS_BACKEND === "true";

// API endpoint configurations
export const API_CONFIG = {
  // Base URLs
  DJANGO_BASE_URL: "http://localhost:8000/api",
  NEXTJS_BASE_URL: "http://localhost:3000/api",

  // Current base URL (can be switched via environment variable)
  BASE_URL: USE_NEXTJS_BACKEND ? "http://localhost:3000/api" : NEXT_PUBLIC_API_BASE_URL,

  // Migration status
  USING_NEXTJS_BACKEND: USE_NEXTJS_BACKEND,
};

// Endpoint mappings for migration
export const ENDPOINTS = {
  // User Authentication (Phase 1 - COMPLETED)
  AUTH: {
    LOGIN: "/users/login/",
    REGISTER: "/users/register/",
    USER_PROFILE: "/users/me/",
    LOGOUT: "/users/logout/", // TODO: Implement in Phase 2
  },

  // User Preferences (Phase 2 - TODO)
  USERS: {
    PREFERENCES: "/users/me/preferences/",
  },

  // Custom Fields (Phase 2 - COMPLETED)
  CUSTOM_FIELDS: {
    LIST: "/workitems/custom-fields/",
    CREATE: "/workitems/custom-fields/",
    DETAIL: "/workitems/custom-fields/{id}/",
    UPDATE: "/workitems/custom-fields/{id}/",
    DELETE: "/workitems/custom-fields/{id}/",
  },

  // Work Items (Phase 3 - TODO)
  WORK_ITEMS: {
    LIFE_ASPECTS: "/workitems/life-aspects/",
    PRIORITY_LEVELS: "/workitems/priority-levels/",
    PROJECTS: "/workitems/projects/",
    PROJECTS_HIERARCHY: "/workitems/projects/hierarchy/",
    OUTCOMES: "/workitems/outcomes/",
  },
} as const;

// Migration status for each endpoint group
export const MIGRATION_STATUS = {
  AUTH: "COMPLETED", // Phase 1 ✅
  CUSTOM_FIELDS: "COMPLETED", // Phase 2 ✅
  PROJECTS: "COMPLETED", // Phase 3 ✅
  OUTCOMES: "COMPLETED", // Phase 3 ✅
  LIFE_ASPECTS: "COMPLETED", // Phase 3 ✅
} as const;

// Helper function to get the full URL for an endpoint
export function getApiUrl(endpoint: string): string {
  return `${API_CONFIG.BASE_URL}${endpoint}`;
}

// Helper function to check if an endpoint is migrated
export function isEndpointMigrated(endpointGroup: keyof typeof MIGRATION_STATUS): boolean {
  return MIGRATION_STATUS[endpointGroup] === "COMPLETED";
}

// Helper function to get migration-aware endpoint URL
export function getMigrationAwareUrl(endpoint: string, endpointGroup: keyof typeof MIGRATION_STATUS): string {
  if (API_CONFIG.USING_NEXTJS_BACKEND && isEndpointMigrated(endpointGroup)) {
    return `${API_CONFIG.NEXTJS_BASE_URL}${endpoint}`;
  }
  return `${API_CONFIG.DJANGO_BASE_URL}${endpoint}`;
}

// Configuration for development and testing
export const DEV_CONFIG = {
  // Enable detailed logging for API calls during migration
  ENABLE_API_LOGGING: process.env.NODE_ENV === "development",

  // Test endpoints for validation
  TEST_ENDPOINTS: {
    HEALTH_CHECK: "/health/",
    AUTH_TEST: "/users/me/",
  },
};

// Export current configuration for easy access
export default API_CONFIG;
