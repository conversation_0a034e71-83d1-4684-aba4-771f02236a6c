import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/database/prisma";
import { verifyToken, extractTokenFromHeader } from "@/lib/auth/jwt";

// Helper function to authenticate request
async function authenticateRequest(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  const token = extractTokenFromHeader(authHeader);

  if (!token) {
    return { error: NextResponse.json({ message: "Authentication required" }, { status: 401 }) };
  }

  const payload = verifyToken(token);
  if (!payload) {
    return { error: NextResponse.json({ message: "Invalid or expired token" }, { status: 401 }) };
  }

  return { userId: payload.userId };
}

// Default preferences structure
const DEFAULT_PREFERENCES = {
  // Custom field display preferences
  custom_field_preferences: {
    // Format: { field_id: { pinned: boolean, visible: boolean } }
  },

  // Table display preferences
  table_preferences: {
    show_completed_outcomes: true,
    show_sub_projects: true,
    compact_view: false,
    sort_by: "created_at",
    sort_order: "asc",
  },

  // Dashboard preferences
  dashboard_preferences: {
    show_recent_projects: true,
    show_upcoming_deadlines: true,
    show_progress_charts: true,
    default_time_range: "30_days",
  },

  // Notification preferences
  notification_preferences: {
    email_notifications: true,
    deadline_reminders: true,
    project_updates: true,
  },
};

// GET /api/users/me/preferences/ - Get user preferences
export async function GET(request: NextRequest) {
  try {
    console.log("GET /api/users/me/preferences/ called");

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Try to find existing preferences
    let preferences = await prisma.auth_user_preferences.findUnique({
      where: { user_id: auth.userId },
    });

    // If no preferences exist, create default ones
    if (!preferences) {
      console.log("No preferences found, creating default preferences for user:", auth.userId);

      preferences = await prisma.auth_user_preferences.create({
        data: {
          user_id: auth.userId,
          preferences: DEFAULT_PREFERENCES,
        },
      });
    }

    console.log("Retrieved preferences for user:", auth.userId);

    return NextResponse.json({
      id: preferences.id,
      user_id: preferences.user_id,
      preferences_data: preferences.preferences,
      week_starts_on: preferences.week_starts_on,
      enable_inheritance: preferences.enable_inheritance,
      created_at: preferences.created_at,
      updated_at: preferences.updated_at,
    });
  } catch (error) {
    console.error("Error fetching user preferences:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// PUT /api/users/me/preferences/ - Update user preferences
export async function PUT(request: NextRequest) {
  try {
    console.log("PUT /api/users/me/preferences/ called");

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Parse request body
    const body = await request.json();
    console.log("Request body:", body);

    // Validate that preferences_data object exists (maintaining backward compatibility with 'preferences')
    const preferencesData = body.preferences_data || body.preferences;
    if (!preferencesData || typeof preferencesData !== "object") {
      return NextResponse.json(
        {
          message: "Invalid preferences data",
        },
        { status: 400 }
      );
    }

    // Validate week_starts_on if provided
    if (body.week_starts_on !== undefined) {
      if (!["Sunday", "Monday"].includes(body.week_starts_on)) {
        return NextResponse.json(
          {
            message: 'week_starts_on must be either "Sunday" or "Monday"',
          },
          { status: 400 }
        );
      }
    }

    // Validate enable_inheritance if provided
    if (body.enable_inheritance !== undefined) {
      if (typeof body.enable_inheritance !== "boolean") {
        return NextResponse.json(
          {
            message: "enable_inheritance must be a boolean",
          },
          { status: 400 }
        );
      }
    }

    // Update or create preferences
    const updateData: any = {
      preferences: preferencesData,
      updated_at: new Date(),
    };

    const createData: any = {
      user_id: auth.userId,
      preferences: preferencesData,
    };

    // Add week_starts_on if provided
    if (body.week_starts_on !== undefined) {
      updateData.week_starts_on = body.week_starts_on;
      createData.week_starts_on = body.week_starts_on;
    }

    // Add enable_inheritance if provided
    if (body.enable_inheritance !== undefined) {
      updateData.enable_inheritance = body.enable_inheritance;
      createData.enable_inheritance = body.enable_inheritance;
    }

    const preferences = await prisma.auth_user_preferences.upsert({
      where: { user_id: auth.userId },
      update: updateData,
      create: createData,
    });

    console.log("Updated preferences for user:", auth.userId);

    return NextResponse.json({
      id: preferences.id,
      user_id: preferences.user_id,
      preferences_data: preferences.preferences,
      week_starts_on: preferences.week_starts_on,
      enable_inheritance: preferences.enable_inheritance,
      created_at: preferences.created_at,
      updated_at: preferences.updated_at,
    });
  } catch (error) {
    console.error("Error updating user preferences:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// PATCH /api/users/me/preferences/ - Partially update user preferences
export async function PATCH(request: NextRequest) {
  try {
    console.log("PATCH /api/users/me/preferences/ called");

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Parse request body
    const body = await request.json();
    console.log("Request body:", body);

    // Validate week_starts_on if provided
    if (body.week_starts_on !== undefined) {
      if (!["Sunday", "Monday"].includes(body.week_starts_on)) {
        return NextResponse.json(
          {
            message: 'week_starts_on must be either "Sunday" or "Monday"',
          },
          { status: 400 }
        );
      }
    }

    // Validate enable_inheritance if provided
    if (body.enable_inheritance !== undefined) {
      if (typeof body.enable_inheritance !== "boolean") {
        return NextResponse.json(
          {
            message: "enable_inheritance must be a boolean",
          },
          { status: 400 }
        );
      }
    }

    // Get existing preferences or create default
    let existingPreferences = await prisma.auth_user_preferences.findUnique({
      where: { user_id: auth.userId },
    });

    if (!existingPreferences) {
      existingPreferences = await prisma.auth_user_preferences.create({
        data: {
          user_id: auth.userId,
          preferences: DEFAULT_PREFERENCES,
        },
      });
    }

    // Prepare update data
    const updateData: any = {
      updated_at: new Date(),
    };

    // Merge existing preferences with updates if preferences_data or preferences provided
    const preferencesData = body.preferences_data || body.preferences;
    if (preferencesData) {
      updateData.preferences = {
        ...(existingPreferences.preferences as object),
        ...preferencesData,
      };
    }

    // Update week_starts_on if provided
    if (body.week_starts_on !== undefined) {
      updateData.week_starts_on = body.week_starts_on;
    }

    // Update enable_inheritance if provided
    if (body.enable_inheritance !== undefined) {
      updateData.enable_inheritance = body.enable_inheritance;
    }

    // Update preferences
    const preferences = await prisma.auth_user_preferences.update({
      where: { user_id: auth.userId },
      data: updateData,
    });

    console.log("Partially updated preferences for user:", auth.userId);

    return NextResponse.json({
      id: preferences.id,
      user_id: preferences.user_id,
      preferences_data: preferences.preferences,
      week_starts_on: preferences.week_starts_on,
      enable_inheritance: preferences.enable_inheritance,
      created_at: preferences.created_at,
      updated_at: preferences.updated_at,
    });
  } catch (error) {
    console.error("Error partially updating user preferences:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
