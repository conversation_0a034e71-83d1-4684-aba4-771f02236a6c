"use client";

import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  Alert,
  Divider,
  CircularProgress,
  IconButton,
} from "@mui/material";
import { Close as CloseIcon } from "@mui/icons-material";
import { projectsService } from "@/lib/api/projectsService";
import { customFieldsService } from "@/lib/api/customFieldsService";
import { Project, UpdateProjectData } from "@/lib/types/projects";
import { CustomFieldDefinition, CustomFieldInput } from "@/lib/types/customFields";
import { formatDateForApi, apiDateToDateObject, dateObjectToApiFormat } from "@/lib/utils/dateUtils";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import CustomFieldsForm from "./CustomFieldsForm";

interface EditProjectModalProps {
  open: boolean;
  projectId: string;
  onClose: () => void;
  onSuccess: () => void;
}

const EditProjectModal: React.FC<EditProjectModalProps> = ({ open, projectId, onClose, onSuccess }) => {
  // Form state
  const [projectName, setProjectName] = useState("");
  const [projectDescription, setProjectDescription] = useState("");
  const [startDate, setStartDate] = useState<string | null>(null);
  const [endDate, setEndDate] = useState<string | null>(null);
  const [customFieldInputs, setCustomFieldInputs] = useState<CustomFieldInput[]>([]);

  // Data state
  const [project, setProject] = useState<Project | null>(null);
  const [customFieldDefinitions, setCustomFieldDefinitions] = useState<CustomFieldDefinition[]>([]);

  // UI state
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load project data and custom field definitions
  useEffect(() => {
    if (open && projectId) {
      loadData();
    }
  }, [open, projectId]);

  const loadData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Load project data and custom field definitions in parallel
      const [projectResponse, customFieldsResponse] = await Promise.all([
        projectsService.getProject(projectId),
        customFieldsService.getCustomFieldDefinitions(),
      ]);

      // Debug: Log the project data received from API
      console.log("🔍 DEBUG: Project data received from API:", {
        id: projectResponse.id,
        name: projectResponse.name,
        start_date: projectResponse.start_date,
        end_date: projectResponse.end_date,
        fullResponse: projectResponse,
      });

      setProject(projectResponse);
      setProjectName(projectResponse.name);
      setProjectDescription(projectResponse.description || "");
      setStartDate(projectResponse.start_date || null);
      setEndDate(projectResponse.end_date || null);

      // Debug: Log the state values after setting
      console.log("🔍 DEBUG: State values after setting:", {
        startDate: projectResponse.start_date || null,
        endDate: projectResponse.end_date || null,
      });

      // Set custom field definitions
      const definitions: CustomFieldDefinition[] = Array.isArray(customFieldsResponse)
        ? customFieldsResponse
        : (customFieldsResponse as any).results || [];

      // Debug logging for custom fields
      console.log("🔍 EditProjectModal - Custom Fields Response:", customFieldsResponse);
      console.log("🔍 EditProjectModal - Processed Definitions:", definitions);

      setCustomFieldDefinitions(definitions);

      // Initialize custom field inputs with current values
      const initialInputs: CustomFieldInput[] = definitions.map((definition: CustomFieldDefinition) => {
        const existingField = projectResponse.resolved_custom_fields?.find((field) => field.definition_id === definition.id);
        return {
          definition_id: definition.id,
          value: existingField?.value || null,
        };
      });
      setCustomFieldInputs(initialInputs);
    } catch (err) {
      console.error("Failed to load project data:", err);
      setError("Failed to load project data. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  // Handle form submission
  const handleSave = async () => {
    if (!project) return;

    setSaving(true);
    setError(null);

    try {
      // Convert date fields to standard API format (YYYY-MM-DD) for best practices
      // Note: Backend now accepts multiple formats, but we send ISO format for consistency
      const processedCustomFieldInputs = customFieldInputs.map((input) => {
        const fieldDefinition = customFieldDefinitions.find((def) => def.id === input.definition_id);

        // Ensure definition_id is a string (convert from number if needed)
        const stringDefinitionId = String(input.definition_id);

        if (fieldDefinition && (fieldDefinition.field_type === "DATE" || fieldDefinition.field_type === "DATETIME")) {
          // Convert display format (DD/MM/YY) to standard API format (YYYY-MM-DD)
          if (typeof input.value === "string" && input.value) {
            const apiFormattedDate = formatDateForApi(input.value);

            // Only log conversion in development for debugging
            if (process.env.NODE_ENV === "development") {
              console.log(`📅 Converting ${fieldDefinition.field_type} field "${fieldDefinition.name}":`, `"${input.value}" → "${apiFormattedDate}"`);
            }

            return {
              ...input,
              definition_id: stringDefinitionId,
              value: apiFormattedDate,
            };
          }
        }

        return {
          ...input,
          definition_id: stringDefinitionId,
        };
      });

      // Debug: Log current state values before creating payload
      console.log("🔍 DEBUG: Current state values before save:", {
        startDate,
        endDate,
        projectName: projectName.trim(),
        description: projectDescription.trim(),
      });

      // Prepare update data with processed custom field inputs
      const updateData: UpdateProjectData = {
        name: projectName.trim(),
        description: projectDescription.trim() || undefined,
        start_date: startDate || undefined,
        end_date: endDate || undefined,
        custom_field_inputs: processedCustomFieldInputs,
      };

      // Log payload in development for verification
      console.log("📤 API Payload:", JSON.stringify(updateData, null, 2));

      // Update project
      await projectsService.updateProject(project.id, updateData);

      // Success - close modal and refresh data
      onSuccess();
    } catch (err: any) {
      console.error("Failed to update project:", err);
      setError(err.message || "Failed to update project. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!loading && !saving) {
      // Reset form state
      setProjectName("");
      setProjectDescription("");
      setStartDate(null);
      setEndDate(null);
      setCustomFieldInputs([]);
      setProject(null);
      setCustomFieldDefinitions([]);
      setError(null);
      onClose();
    }
  };

  // Handle custom fields change
  const handleCustomFieldsChange = (inputs: CustomFieldInput[]) => {
    // Debug logging to track state changes
    if (process.env.NODE_ENV === "development") {
      console.log(`🔍 DEBUG: Custom fields state updated:`, inputs);

      // Specifically log date fields
      const dateFields = inputs.filter((input) => {
        const definition = customFieldDefinitions.find((def) => def.id === input.definition_id);
        return definition && definition.field_type === "DATE";
      });

      if (dateFields.length > 0) {
        console.log(`📅 DEBUG: Date fields in state:`, dateFields);
      }
    }

    setCustomFieldInputs(inputs);
  };

  // Create merged current values that combines original resolved fields with any updates
  const mergedCurrentValues = React.useMemo(() => {
    if (!project?.resolved_custom_fields) return [];

    // Start with original resolved fields
    const merged = [...project.resolved_custom_fields];

    // Apply any updates from customFieldInputs
    customFieldInputs.forEach((input) => {
      const existingIndex = merged.findIndex((field) => field.definition_id === input.definition_id);
      if (existingIndex >= 0) {
        // Update existing field
        merged[existingIndex] = {
          ...merged[existingIndex],
          value: input.value,
        };
      } else {
        // Add new field (shouldn't happen in edit mode, but just in case)
        const definition = customFieldDefinitions.find((def) => def.id === input.definition_id);
        if (definition) {
          merged.push({
            definition_id: input.definition_id,
            definition_name: definition.name,
            field_type: definition.field_type,
            is_required: definition.is_required,
            sort_order: definition.sort_order,
            value: input.value,
            display_value: String(input.value || ""),
          });
        }
      }
    });

    return merged;
  }, [project?.resolved_custom_fields, customFieldInputs, customFieldDefinitions]);

  // Validation
  const isFormValid = projectName.trim().length > 0;
  const canSave = isFormValid && !loading && !saving;

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth slotProps={{ paper: { sx: { borderRadius: 2 } } }}>
      <DialogTitle sx={{ pb: 1 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6" component="h2">
            Edit Project
          </Typography>
          <IconButton onClick={handleClose} disabled={loading || saving} sx={{ p: 1 }}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ pt: 2 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {loading ? (
          <Box display="flex" justifyContent="center" alignItems="center" py={4}>
            <CircularProgress />
            <Typography variant="body2" sx={{ ml: 2 }}>
              Loading project data...
            </Typography>
          </Box>
        ) : (
          <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
            {/* Standard Project Fields */}
            <Box>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 500 }}>
                Project Details
              </Typography>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <TextField
                  label="Project Name"
                  value={projectName}
                  onChange={(e) => setProjectName(e.target.value)}
                  required
                  disabled={saving}
                  fullWidth
                  variant="outlined"
                  size="small"
                  error={!projectName.trim()}
                  helperText={!projectName.trim() ? "Project name is required" : ""}
                />
                <TextField
                  label="Description"
                  value={projectDescription}
                  onChange={(e) => setProjectDescription(e.target.value)}
                  disabled={saving}
                  fullWidth
                  multiline
                  rows={3}
                  variant="outlined"
                  size="small"
                  placeholder="Optional project description..."
                />

                {/* Start Date and End Date Fields */}
                <Box sx={{ display: "flex", gap: 2 }}>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DatePicker
                      label="Start Date"
                      value={apiDateToDateObject(startDate)}
                      onChange={(date) => {
                        const formattedDate = dateObjectToApiFormat(date);
                        console.log("🗓️ Start Date changed:", { date, formattedDate });
                        setStartDate(formattedDate);
                      }}
                      disabled={saving}
                      format="E, d MMM, yy"
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          size: "small",
                          placeholder: "Mon, 16 Jun, 25",
                        },
                      }}
                    />
                  </LocalizationProvider>
                  <LocalizationProvider dateAdapter={AdapterDateFns}>
                    <DatePicker
                      label="End Date"
                      value={apiDateToDateObject(endDate)}
                      onChange={(date) => {
                        const formattedDate = dateObjectToApiFormat(date);
                        console.log("🗓️ End Date changed:", { date, formattedDate });
                        setEndDate(formattedDate);
                      }}
                      disabled={saving}
                      format="E, d MMM, yy"
                      slotProps={{
                        textField: {
                          fullWidth: true,
                          size: "small",
                          placeholder: "Mon, 16 Jun, 25",
                        },
                      }}
                    />
                  </LocalizationProvider>
                </Box>
              </Box>
            </Box>

            {/* Custom Fields - Always show section */}
            <Divider />
            <Box>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 500 }}>
                Custom Fields
              </Typography>
              {customFieldDefinitions.length > 0 ? (
                <CustomFieldsForm
                  customFieldDefinitions={customFieldDefinitions}
                  currentValues={mergedCurrentValues}
                  onChange={handleCustomFieldsChange}
                  disabled={saving}
                />
              ) : (
                <Typography variant="body2" color="text.secondary" sx={{ fontStyle: "italic" }}>
                  No custom fields have been defined for projects yet. You can create custom fields in Settings.
                </Typography>
              )}
            </Box>
          </Box>
        )}
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 2 }}>
        <Button onClick={handleClose} disabled={loading || saving} color="inherit">
          Cancel
        </Button>
        <Button onClick={handleSave} disabled={!canSave} variant="contained" startIcon={saving ? <CircularProgress size={16} /> : undefined}>
          {saving ? "Saving..." : "Save Changes"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default EditProjectModal;
