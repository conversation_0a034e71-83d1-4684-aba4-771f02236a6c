/**
 * User Preferences Types
 * Based on USER_PREFERENCES_API.md and USER_PREFERENCES_IMPLEMENTATION_SUMMARY.md
 */

// Project view field preferences
export interface ProjectViewFieldsPreferences {
  pinned_field_id: string | null;
  hidden_field_ids: string[];
}

// Main preferences data structure
export interface UserPreferencesData {
  project_view_fields?: ProjectViewFieldsPreferences;
}

// Complete user preferences response from API
export interface UserPreferences {
  id: number;
  user_id: number;
  preferences_data: UserPreferencesData;
  week_starts_on: "Sunday" | "Monday";
  enable_inheritance: boolean;
  created_at: string;
  updated_at: string;
}

// Request payload for updating preferences
export interface UpdateUserPreferencesRequest {
  preferences: UserPreferencesData;
}

// API response types
export interface UserPreferencesResponse {
  preferences_data: UserPreferencesData;
  week_starts_on: "Sunday" | "Monday";
  enable_inheritance: boolean;
  created_at: string;
  updated_at: string;
}

// Error types
export interface UserPreferencesError {
  message: string;
  field?: string;
  code?: string;
}

// UI state types for the customize view modal
export interface CustomizeViewModalState {
  isOpen: boolean;
  loading: boolean;
  saving: boolean;
  error: string | null;
  preferences: UserPreferences | null;
  customFields: any[]; // Will be typed as CustomFieldDefinition[]
}

// Form data for the customize view modal
export interface CustomizeViewFormData {
  pinnedFieldId: string | null;
  hiddenFieldIds: string[];
}
