"use client";

import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  TextField,
  FormControlLabel,
  Switch,
  Box,
  Typography,
  Alert,
  Divider,
  CircularProgress,
  Chip,
} from "@mui/material";
import { Close as CloseIcon, Delete as DeleteIcon } from "@mui/icons-material";
import { customFieldsService } from "@/lib/api/customFieldsService";
import { CustomFieldDefinition, UpdateCustomFieldDefinitionData, UpdateCustomFieldChoiceOptionData, FIELD_TYPES } from "@/lib/types/customFields";
import ChoiceOptionsManager from "./ChoiceOptionsManager";

interface EditCustomFieldModalProps {
  open: boolean;
  definition: CustomFieldDefinition | null;
  onClose: () => void;
  onSuccess: () => void;
}

const EditCustomFieldModal: React.FC<EditCustomFieldModalProps> = ({ open, definition, onClose, onSuccess }) => {
  // Form state
  const [formData, setFormData] = useState<UpdateCustomFieldDefinitionData>({
    name: "",
    field_type: "TEXT",
    is_required: false,
    sort_order: 0,
    choice_options: [],
  });

  // UI state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Get field type info
  const fieldTypeInfo = definition ? FIELD_TYPES.find((type) => type.type === definition.field_type) : null;

  // Initialize form data when definition changes
  useEffect(() => {
    if (definition) {
      setFormData({
        name: definition.name,
        field_type: definition.field_type,
        is_required: definition.is_required,
        sort_order: definition.sort_order,
        choice_options:
          definition.choice_options?.map((option) => ({
            id: option.id, // CRITICAL: Preserve ID for Smart Matching Logic
            value: option.value,
            color: option.color,
            sort_order: option.sort_order,
            is_default: option.is_default, // Include default flag
          })) || [],
      });
      setError(null);
      setShowDeleteConfirm(false);
    }
  }, [definition]);

  // Handle form field changes
  const handleFieldChange = (field: keyof UpdateCustomFieldDefinitionData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
    setError(null);
  };

  // Handle choice options change
  const handleChoiceOptionsChange = (options: UpdateCustomFieldChoiceOptionData[]) => {
    setFormData((prev) => ({
      ...prev,
      choice_options: options,
    }));
  };

  // Validate form
  const validateForm = (): string | null => {
    if (!formData.name?.trim()) {
      return "Field name is required";
    }

    if (formData.name.length > 100) {
      return "Field name must be 100 characters or less";
    }

    if (fieldTypeInfo?.hasChoices && (!formData.choice_options || formData.choice_options.length === 0)) {
      return "At least one choice option is required for select fields";
    }

    return null;
  };

  // Validate payload before sending to backend
  const validatePayload = (data: UpdateCustomFieldDefinitionData): string | null => {
    // Check for required fields
    if (!data.name?.trim()) return "Name is required";
    if (!data.field_type) return "Field type is required";
    if (typeof data.is_required !== "boolean") return "is_required must be a boolean";
    if (typeof data.sort_order !== "number") return "sort_order must be a number";

    // Validate choice options for select fields
    if (fieldTypeInfo?.hasChoices) {
      if (!data.choice_options || data.choice_options.length === 0) {
        return "At least one choice option is required for select fields";
      }

      // Validate each choice option
      for (let i = 0; i < data.choice_options.length; i++) {
        const option = data.choice_options[i];
        if (!option.value?.trim()) return `Choice option ${i + 1} value is required`;
        if (!option.color) return `Choice option ${i + 1} color is required`;
        if (typeof option.sort_order !== "number") return `Choice option ${i + 1} sort_order must be a number`;
      }
    }

    return null;
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!definition) return;

    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    // Additional payload validation
    const payloadError = validatePayload(formData);
    if (payloadError) {
      setError(`Payload validation failed: ${payloadError}`);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log("🔍 Frontend validation passed, sending update request...");
      console.log("📋 Form data being sent:", formData);

      // According to USING_CUSTOM_APIS.md, PUT requests require ALL required fields
      // formData now contains all required fields
      await customFieldsService.updateCustomFieldDefinition(definition.id, formData);
      onSuccess();
    } catch (err: any) {
      console.error("❌ Error updating custom field:", err);

      // Enhanced error handling for different error types
      let errorMessage = "Failed to update custom field";

      if (err.response?.status === 400) {
        // Handle validation errors from backend
        const responseData = err.response.data;

        if (responseData?.choice_options) {
          // Handle choice option specific errors
          if (Array.isArray(responseData.choice_options)) {
            errorMessage = `Choice option error: ${responseData.choice_options[0]}`;
          } else if (typeof responseData.choice_options === "string") {
            errorMessage = `Choice option error: ${responseData.choice_options}`;
          } else {
            errorMessage = "Invalid choice option data";
          }
        } else if (responseData?.detail) {
          errorMessage = responseData.detail;
        } else if (responseData?.message) {
          errorMessage = responseData.message;
        } else if (typeof responseData === "string") {
          errorMessage = responseData;
        } else {
          // Handle field-specific validation errors
          const firstErrorKey = Object.keys(responseData || {})[0];
          if (firstErrorKey && responseData[firstErrorKey]) {
            const firstError = Array.isArray(responseData[firstErrorKey]) ? responseData[firstErrorKey][0] : responseData[firstErrorKey];
            errorMessage = `${firstErrorKey}: ${firstError}`;
          }
        }
      } else if (err.response?.status === 500) {
        errorMessage = "Server error occurred while updating the custom field. Please check the server logs for details.";
      } else if (err.message) {
        errorMessage = err.message;
      }

      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Handle delete
  const handleDelete = async () => {
    if (!definition) return;

    try {
      setLoading(true);
      setError(null);

      await customFieldsService.deleteCustomFieldDefinition(definition.id);
      onSuccess();
    } catch (err: any) {
      console.error("Error deleting custom field:", err);
      setError(err.message || "Failed to delete custom field");
    } finally {
      setLoading(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!loading) {
      setShowDeleteConfirm(false);
      setError(null);
      onClose();
    }
  };

  if (!definition) {
    return null;
  }

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 },
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6" component="h2">
            Edit Custom Field
          </Typography>
          <Button onClick={handleClose} disabled={loading} sx={{ minWidth: "auto", p: 1 }}>
            <CloseIcon />
          </Button>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ pt: 2 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Field Information */}
        <Box mb={3}>
          <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 500 }}>
            Field Information
          </Typography>

          <Box display="flex" gap={2} mb={2}>
            <Chip label={fieldTypeInfo?.label || definition.field_type} variant="outlined" sx={{ borderRadius: 1 }} />
          </Box>

          <TextField
            fullWidth
            label="Field Name"
            value={formData.name}
            onChange={(e) => handleFieldChange("name", e.target.value)}
            sx={{ mb: 2 }}
            disabled={loading}
          />

          <FormControlLabel
            control={
              <Switch checked={formData.is_required} onChange={(e) => handleFieldChange("is_required", e.target.checked)} disabled={loading} />
            }
            label="Required Field"
          />
        </Box>

        {/* Choice Options for SELECT fields */}
        {fieldTypeInfo?.hasChoices && (
          <>
            <Divider sx={{ my: 3 }} />
            <Box>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 500 }}>
                Choice Options
              </Typography>
              <ChoiceOptionsManager
                options={formData.choice_options || []}
                onChange={handleChoiceOptionsChange}
                disabled={loading}
                fieldType={definition.field_type}
              />
            </Box>
          </>
        )}

        {/* Delete Confirmation */}
        {showDeleteConfirm && (
          <>
            <Divider sx={{ my: 3 }} />
            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Are you sure you want to delete this custom field?
              </Typography>
              <Typography variant="body2">This action cannot be undone. All data associated with this field will be permanently removed.</Typography>
            </Alert>
          </>
        )}
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3, justifyContent: "space-between" }}>
        {/* Delete Button */}
        <Box>
          {!showDeleteConfirm ? (
            <Button onClick={() => setShowDeleteConfirm(true)} disabled={loading} color="error" startIcon={<DeleteIcon />}>
              Delete Field
            </Button>
          ) : (
            <Box display="flex" gap={1}>
              <Button onClick={() => setShowDeleteConfirm(false)} disabled={loading} size="small">
                Cancel
              </Button>
              <Button
                onClick={handleDelete}
                disabled={loading}
                color="error"
                variant="contained"
                size="small"
                startIcon={loading ? <CircularProgress size={16} /> : <DeleteIcon />}
              >
                {loading ? "Deleting..." : "Confirm Delete"}
              </Button>
            </Box>
          )}
        </Box>

        {/* Save/Cancel Buttons */}
        <Box display="flex" gap={1}>
          <Button onClick={handleClose} disabled={loading} color="inherit">
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading || !formData.name?.trim() || showDeleteConfirm}
            startIcon={loading ? <CircularProgress size={16} /> : null}
          >
            {loading ? "Saving..." : "Save Changes"}
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  );
};

export default EditCustomFieldModal;
