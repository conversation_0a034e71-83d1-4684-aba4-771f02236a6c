/**
 * Priority Levels Types
 * Based on API documentation for /api/workitems/priority-levels/
 */

export interface PriorityLevel {
  id: string; // UUID from backend
  name: string;
  description?: string;
  color?: string; // Hex color code from backend (optional, can be null)
  display_order: number; // For requests
  sort_order?: number; // From API responses (same as display_order)
  created_at: string;
  updated_at: string;
  user: string; // User UUID from backend
}

export interface CreatePriorityLevelData {
  name: string;
  description?: string;
  color?: string; // Optional hex color code
  display_order?: number; // Optional as per API docs - auto-assigned if not provided
}

export interface UpdatePriorityLevelData {
  name?: string;
  description?: string;
  color?: string; // Hex color code
  display_order?: number;
}

export interface PriorityLevelError {
  message: string;
  field?: string;
  details?: Record<string, string[]>;
}

// Paginated response type for priority levels
export interface PaginatedPriorityLevelsResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: PriorityLevel[];
}

// Default Priority Levels as per API documentation
export const DEFAULT_PRIORITY_LEVELS = [
  { name: "Urgent and Uplifting", description: "High priority, personally fulfilling", display_order: 1 },
  { name: "Urgent and Not Uplifting", description: "High priority, less fulfilling", display_order: 2 },
  { name: "Not Urgent and Uplifting", description: "Important for growth, not time-sensitive", display_order: 3 },
  { name: "Not Urgent and Not Uplifting", description: "Lower priority items", display_order: 4 },
] as const;

// Priority level display configuration for UI
export interface PriorityDisplayConfig {
  icon: string; // Material Icon name
  color: string; // MUI color or hex
  chipVariant: "filled" | "outlined";
}

// Mapping priority levels to display configuration
export const PRIORITY_DISPLAY_CONFIG: Record<string, PriorityDisplayConfig> = {
  "Urgent and Uplifting": {
    icon: "warning_amber",
    color: "error",
    chipVariant: "filled",
  },
  "Urgent and Not Uplifting": {
    icon: "priority_high",
    color: "warning",
    chipVariant: "filled",
  },
  "Not Urgent and Uplifting": {
    icon: "trending_up",
    color: "info",
    chipVariant: "outlined",
  },
  "Not Urgent and Not Uplifting": {
    icon: "remove",
    color: "default",
    chipVariant: "outlined",
  },
  // Fallback for custom priority levels
  default: {
    icon: "circle",
    color: "default",
    chipVariant: "outlined",
  },
};

// Helper function to get priority display config
export function getPriorityDisplayConfig(priorityName: string): PriorityDisplayConfig {
  return PRIORITY_DISPLAY_CONFIG[priorityName] || PRIORITY_DISPLAY_CONFIG["default"];
}

// Helper function to get priority icon name
export function getPriorityIcon(priorityName: string): string {
  return getPriorityDisplayConfig(priorityName).icon;
}

// Helper function to get priority color
export function getPriorityColor(priorityName: string): string {
  return getPriorityDisplayConfig(priorityName).color;
}
