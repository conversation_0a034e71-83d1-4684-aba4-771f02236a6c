"use client";

import React from "react";
import { DatePicker as MuiDatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { apiDateToDateObject, dateObjectToApiFormat } from "@/lib/utils/dateUtils";

interface DatePickerProps {
  label?: string;
  value?: string | null;
  onChange: (value: string | null) => void;
  disabled?: boolean;
  required?: boolean;
  error?: boolean;
  helperText?: string;
  fullWidth?: boolean;
  size?: "small" | "medium";
  sx?: any;
}

const DatePicker: React.FC<DatePickerProps> = ({
  label,
  value,
  onChange,
  disabled = false,
  required = false,
  error = false,
  helperText,
  fullWidth = true,
  size = "small",
  sx,
}) => {
  const handleDateChange = (date: Date | null) => {
    const apiValue = dateObjectToApiFormat(date);
    onChange(apiValue);
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <MuiDatePicker
        label={label}
        value={apiDateToDateObject(value)}
        onChange={handleDateChange}
        disabled={disabled}
        format="E, d MMM, yy"
        slotProps={{
          textField: {
            fullWidth,
            size,
            required,
            error,
            helperText,
            placeholder: "Mon, 16 Jun, 25",
            sx,
          },
        }}
      />
    </LocalizationProvider>
  );
};

export default DatePicker;
