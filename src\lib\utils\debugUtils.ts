/**
 * Debug utilities for troubleshooting API issues
 */

export interface ApiRequestDebugInfo {
  url: string;
  method: string;
  headers: Record<string, any>;
  payload: any;
  payloadSize: number;
  timestamp: string;
}

export interface ApiResponseDebugInfo {
  status: number;
  statusText: string;
  headers: Record<string, any>;
  data: any;
  timestamp: string;
}

/**
 * Capture detailed information about an API request for debugging
 */
export const captureRequestDebugInfo = (url: string, method: string, headers: Record<string, any>, payload: any): ApiRequestDebugInfo => {
  return {
    url,
    method: method.toUpperCase(),
    headers: { ...headers },
    payload: JSON.parse(JSON.stringify(payload)), // Deep clone
    payloadSize: JSON.stringify(payload).length,
    timestamp: new Date().toISOString(),
  };
};

/**
 * Capture detailed information about an API response for debugging
 */
export const captureResponseDebugInfo = (status: number, statusText: string, headers: Record<string, any>, data: any): ApiResponseDebugInfo => {
  return {
    status,
    statusText,
    headers: { ...headers },
    data: JSON.parse(JSON.stringify(data)), // Deep clone
    timestamp: new Date().toISOString(),
  };
};

/**
 * Format debug information for console output
 */
export const formatDebugInfo = (requestInfo: ApiRequestDebugInfo, responseInfo?: ApiResponseDebugInfo, error?: any): string => {
  const lines = [
    "🔍 API DEBUG INFORMATION",
    "=" * 50,
    "",
    "📤 REQUEST:",
    `  URL: ${requestInfo.url}`,
    `  Method: ${requestInfo.method}`,
    `  Timestamp: ${requestInfo.timestamp}`,
    `  Payload Size: ${requestInfo.payloadSize} characters`,
    "",
    "📋 REQUEST HEADERS:",
    JSON.stringify(requestInfo.headers, null, 2),
    "",
    "📦 REQUEST PAYLOAD:",
    JSON.stringify(requestInfo.payload, null, 2),
  ];

  if (responseInfo) {
    lines.push(
      "",
      "📥 RESPONSE:",
      `  Status: ${responseInfo.status} ${responseInfo.statusText}`,
      `  Timestamp: ${responseInfo.timestamp}`,
      "",
      "📋 RESPONSE HEADERS:",
      JSON.stringify(responseInfo.headers, null, 2),
      "",
      "📦 RESPONSE DATA:",
      JSON.stringify(responseInfo.data, null, 2)
    );
  }

  if (error) {
    lines.push(
      "",
      "❌ ERROR:",
      `  Message: ${error.message || "Unknown error"}`,
      `  Stack: ${error.stack || "No stack trace"}`,
      "",
      "🔧 ERROR DETAILS:",
      JSON.stringify(error, null, 2)
    );
  }

  lines.push("", "=" * 50);
  return lines.join("\n");
};

/**
 * Validate custom field definition payload structure
 */
export const validateCustomFieldPayload = (payload: any): string[] => {
  const errors: string[] = [];

  // Required fields validation
  if (!payload.name || typeof payload.name !== "string") {
    errors.push("name must be a non-empty string");
  }

  if (!payload.field_type || typeof payload.field_type !== "string") {
    errors.push("field_type must be a non-empty string");
  }

  // target_model is no longer required in universal custom fields system

  if (typeof payload.is_required !== "boolean") {
    errors.push("is_required must be a boolean");
  }

  if (typeof payload.sort_order !== "number") {
    errors.push("sort_order must be a number");
  }

  // Choice options validation
  if (payload.choice_options) {
    if (!Array.isArray(payload.choice_options)) {
      errors.push("choice_options must be an array");
    } else {
      payload.choice_options.forEach((option: any, index: number) => {
        if (!option.value || typeof option.value !== "string") {
          errors.push(`choice_options[${index}].value must be a non-empty string`);
        }
        if (!option.color || typeof option.color !== "string") {
          errors.push(`choice_options[${index}].color must be a non-empty string`);
        }
        if (typeof option.sort_order !== "number") {
          errors.push(`choice_options[${index}].sort_order must be a number`);
        }
        // ID is optional - present for existing options, absent for new options
        if (option.id && typeof option.id !== "string") {
          errors.push(`choice_options[${index}].id must be a string if provided`);
        }
      });
    }
  }

  return errors;
};

/**
 * Generate a detailed error report for backend developers
 */
export const generateBackendErrorReport = (requestInfo: ApiRequestDebugInfo, responseInfo: ApiResponseDebugInfo, error: any): string => {
  const payloadValidationErrors = validateCustomFieldPayload(requestInfo.payload);

  return `
BACKEND ERROR REPORT - 500 Internal Server Error
================================================

SUMMARY:
- Endpoint: ${requestInfo.method} ${requestInfo.url}
- Status: ${responseInfo.status} ${responseInfo.statusText}
- Request Time: ${requestInfo.timestamp}
- Response Time: ${responseInfo.timestamp}

FRONTEND PAYLOAD VALIDATION:
${
  payloadValidationErrors.length === 0
    ? "✅ All frontend validations passed"
    : "❌ Frontend validation errors:\n" + payloadValidationErrors.map((e) => `  - ${e}`).join("\n")
}

REQUEST PAYLOAD:
${JSON.stringify(requestInfo.payload, null, 2)}

RESPONSE DATA:
${JSON.stringify(responseInfo.data, null, 2)}

SUGGESTED BACKEND INVESTIGATION AREAS:
1. Check Django server logs for Python traceback
2. Verify serializer validation logic for choice_options
3. Check for database constraint violations
4. Verify permission checks and object ownership
5. Look for unhandled exceptions in view logic

FRONTEND ERROR DETAILS:
${JSON.stringify(error, null, 2)}
`;
};
