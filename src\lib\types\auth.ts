export interface User {
  id: number;
  username: string;
  email: string;
  first_name: string;
  last_name: string;
  date_joined: string;
  is_active: boolean;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  username: string;
  email: string;
  password: string;
  password_confirm: string; // Added to match backend API
  first_name?: string;
  last_name?: string;
}

export interface AuthResponse {
  token: string;
  user?: User; // Made optional as some endpoints might only return token
}

export interface LoginResponse {
  token: string;
}

export interface AuthError {
  message: string;
  field?: string;
  details?: Record<string, string[]>;
}

export interface AuthContextType {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  login: (credentials: LoginCredentials) => Promise<void>;
  logout: () => void;
  register: (userData: RegisterData) => Promise<void>;
  isLoading: boolean;
  error: string | null;
  clearError: () => void;
}
