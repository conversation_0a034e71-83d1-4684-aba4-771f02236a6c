import apiClient from "./apiClient";
import {
  CustomFieldDefinition,
  CreateCustomFieldDefinitionData,
  UpdateCustomFieldDefinitionData,
  CustomFieldDefinitionResponse,
  CustomFieldError,
} from "@/lib/types/customFields";
import { API_ENDPOINTS } from "@/lib/utils/constants";
import { AxiosError } from "axios";
import { captureRequestDebugInfo, captureResponseDebugInfo, generateBackendErrorReport, validateCustomFieldPayload } from "@/lib/utils/debugUtils";

/**
 * Handle API errors and convert them to CustomFieldError format
 */
const handleApiError = (error: AxiosError): CustomFieldError => {
  console.error("Custom Fields API Error:", error);

  if (error.response?.data) {
    const errorData = error.response.data as any;

    if (typeof errorData === "string") {
      return { message: errorData };
    }

    if (errorData.detail) {
      return { message: errorData.detail };
    }

    if (errorData.message) {
      return { message: errorData.message };
    }

    // Handle field-specific errors
    if (typeof errorData === "object") {
      const firstKey = Object.keys(errorData)[0];
      const firstError = errorData[firstKey];

      if (Array.isArray(firstError)) {
        return {
          message: firstError[0],
          field: firstKey,
        };
      }

      return {
        message: firstError || "Validation error",
        field: firstKey,
      };
    }
  }

  return {
    message: error.message || "An unexpected error occurred",
  };
};

export const customFieldsService = {
  /**
   * Get all custom field definitions for the authenticated user
   */
  async getCustomFieldDefinitions(): Promise<CustomFieldDefinition[]> {
    try {
      const url = API_ENDPOINTS.WORK_ITEMS.CUSTOM_FIELDS;

      const response = await apiClient.get<CustomFieldDefinitionResponse | CustomFieldDefinition[]>(url);

      console.log("Custom field definitions API response:", response.data);

      // Handle both paginated and direct array responses
      let definitions: CustomFieldDefinition[];

      if (Array.isArray(response.data)) {
        // Direct array response
        definitions = response.data;
      } else if (response.data && "results" in response.data) {
        // Paginated response
        definitions = response.data.results;
      } else {
        console.warn("Unexpected custom field definitions response format:", response.data);
        definitions = [];
      }

      return definitions;
    } catch (error) {
      console.error("Error fetching custom field definitions:", error);
      throw handleApiError(error as AxiosError);
    }
  },

  /**
   * Get a specific custom field definition by ID
   */
  async getCustomFieldDefinition(id: string): Promise<CustomFieldDefinition> {
    try {
      const response = await apiClient.get<CustomFieldDefinition>(`${API_ENDPOINTS.WORK_ITEMS.CUSTOM_FIELDS}${id}/`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching custom field definition ${id}:`, error);
      throw handleApiError(error as AxiosError);
    }
  },

  /**
   * Create a new custom field definition
   */
  async createCustomFieldDefinition(data: CreateCustomFieldDefinitionData): Promise<CustomFieldDefinition> {
    try {
      console.log("Creating custom field definition:", data);

      const response = await apiClient.post<CustomFieldDefinition>(API_ENDPOINTS.WORK_ITEMS.CUSTOM_FIELDS, data);

      console.log("Custom field definition created:", response.data);
      return response.data;
    } catch (error) {
      console.error("Error creating custom field definition:", error);
      throw handleApiError(error as AxiosError);
    }
  },

  /**
   * Update a custom field definition
   */
  async updateCustomFieldDefinition(id: string, data: UpdateCustomFieldDefinitionData): Promise<CustomFieldDefinition> {
    const url = `${API_ENDPOINTS.WORK_ITEMS.CUSTOM_FIELDS}${id}/`;

    // Capture request debug info
    const requestInfo = captureRequestDebugInfo(url, "PUT", { "Content-Type": "application/json" }, data);

    try {
      console.log(`🔄 Updating custom field definition ${id}`);

      // Frontend payload validation
      const validationErrors = validateCustomFieldPayload(data);
      if (validationErrors.length > 0) {
        console.warn("⚠️ Frontend validation warnings:", validationErrors);
      }

      console.log("📤 Request payload:", JSON.stringify(data, null, 2));
      console.log("📤 Payload size:", JSON.stringify(data).length, "characters");
      console.log("📤 Choice options count:", data.choice_options?.length || 0);

      const response = await apiClient.put<CustomFieldDefinition>(url, data);

      // Capture successful response
      const responseInfo = captureResponseDebugInfo(response.status, response.statusText, response.headers, response.data);

      console.log("✅ Custom field definition updated successfully:", response.data);
      return response.data;
    } catch (error) {
      console.error(`❌ Error updating custom field definition ${id}:`, error);

      // Enhanced error logging for 500 errors
      if (error instanceof Error && "response" in error) {
        const axiosError = error as AxiosError;

        if (axiosError.response?.status === 500) {
          // Capture error response
          const responseInfo = captureResponseDebugInfo(
            axiosError.response.status,
            axiosError.response.statusText,
            axiosError.response.headers,
            axiosError.response.data
          );

          // Generate comprehensive error report
          const errorReport = generateBackendErrorReport(requestInfo, responseInfo, axiosError);

          console.error("🚨 500 INTERNAL SERVER ERROR - COMPREHENSIVE DEBUG REPORT:");
          console.error(errorReport);

          // Also log individual components for easy access
          console.group("🔍 Detailed Error Information");
          console.error("Request URL:", axiosError.config?.url);
          console.error("Request method:", axiosError.config?.method);
          console.error("Request headers:", axiosError.config?.headers);
          console.error("Request data:", axiosError.config?.data);
          console.error("Response status:", axiosError.response?.status);
          console.error("Response headers:", axiosError.response?.headers);
          console.error("Response data:", axiosError.response?.data);
          console.groupEnd();
        }
      }

      throw handleApiError(error as AxiosError);
    }
  },

  /**
   * Partially update a custom field definition
   */
  async patchCustomFieldDefinition(id: string, data: Partial<UpdateCustomFieldDefinitionData>): Promise<CustomFieldDefinition> {
    try {
      console.log(`Patching custom field definition ${id}:`, data);

      const response = await apiClient.patch<CustomFieldDefinition>(`${API_ENDPOINTS.WORK_ITEMS.CUSTOM_FIELDS}${id}/`, data);

      console.log("Custom field definition patched:", response.data);
      return response.data;
    } catch (error) {
      console.error(`Error patching custom field definition ${id}:`, error);
      throw handleApiError(error as AxiosError);
    }
  },

  /**
   * Delete a custom field definition
   */
  async deleteCustomFieldDefinition(id: string): Promise<void> {
    try {
      console.log(`Deleting custom field definition ${id}`);

      await apiClient.delete(`${API_ENDPOINTS.WORK_ITEMS.CUSTOM_FIELDS}${id}/`);

      console.log("Custom field definition deleted successfully");
    } catch (error) {
      console.error(`Error deleting custom field definition ${id}:`, error);
      throw handleApiError(error as AxiosError);
    }
  },

  /**
   * Update sort order for multiple custom field definitions
   */
  async updateSortOrder(definitions: { id: string; sort_order: number }[]): Promise<void> {
    try {
      console.log("Updating sort order for custom field definitions:", definitions);

      // Update each definition's sort order individually
      const updatePromises = definitions.map(({ id, sort_order }) => this.patchCustomFieldDefinition(id, { sort_order }));

      await Promise.all(updatePromises);

      console.log("Sort order updated successfully");
    } catch (error) {
      console.error("Error updating sort order:", error);
      throw handleApiError(error as AxiosError);
    }
  },
};
