"use client";

import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  <PERSON>alog<PERSON>ctions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Box,
  Typography,
  Alert,
  Divider,
  CircularProgress,
} from "@mui/material";
import { Close as CloseIcon } from "@mui/icons-material";
import { customFieldsService } from "@/lib/api/customFieldsService";
import {
  CreateCustomFieldDefinitionData,
  CustomFieldType,
  FIELD_TYPES,
  CreateCustomFieldChoiceOptionData,
  DEFAULT_CHOICE_COLORS,
} from "@/lib/types/customFields";
import FieldTypePreview from "./FieldTypePreview";
import ChoiceOptionsManager from "./ChoiceOptionsManager";

interface CreateCustomFieldModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const CreateCustomFieldModal: React.FC<CreateCustomFieldModalProps> = ({ open, onClose, onSuccess }) => {
  // Form state
  const [formData, setFormData] = useState<CreateCustomFieldDefinitionData>({
    name: "",
    field_type: "TEXT",
    is_required: false,
    choice_options: [],
  });

  // UI state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get selected field type info
  const selectedFieldType = FIELD_TYPES.find((type) => type.type === formData.field_type);

  // Handle form field changes
  const handleFieldChange = (field: keyof CreateCustomFieldDefinitionData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
      // Clear choice options if field type doesn't support them
      ...(field === "field_type" && !FIELD_TYPES.find((t) => t.type === value)?.hasChoices ? { choice_options: [] } : {}),
    }));
    setError(null);
  };

  // Handle choice options change
  const handleChoiceOptionsChange = (options: CreateCustomFieldChoiceOptionData[]) => {
    setFormData((prev) => ({
      ...prev,
      choice_options: options,
    }));
  };

  // Validate form
  const validateForm = (): string | null => {
    if (!formData.name.trim()) {
      return "Field name is required";
    }

    if (formData.name.length > 100) {
      return "Field name must be 100 characters or less";
    }

    if (selectedFieldType?.hasChoices && (!formData.choice_options || formData.choice_options.length === 0)) {
      return "At least one choice option is required for select fields";
    }

    return null;
  };

  // Handle form submission
  const handleSubmit = async () => {
    const validationError = validateForm();
    if (validationError) {
      setError(validationError);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      await customFieldsService.createCustomFieldDefinition(formData);

      // Reset form
      setFormData({
        name: "",
        field_type: "TEXT",
        is_required: false,
        choice_options: [],
      });

      onSuccess();
    } catch (err: any) {
      console.error("Error creating custom field:", err);
      setError(err.message || "Failed to create custom field");
    } finally {
      setLoading(false);
    }
  };

  // Handle modal close
  const handleClose = () => {
    if (!loading) {
      setFormData({
        name: "",
        field_type: "TEXT",
        is_required: false,
        choice_options: [],
      });
      setError(null);
      onClose();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 },
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6" component="h2">
            Create Custom Field
          </Typography>
          <Button onClick={handleClose} disabled={loading} sx={{ minWidth: "auto", p: 1 }}>
            <CloseIcon />
          </Button>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ pt: 2 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {/* Basic Information */}
        <Box mb={3}>
          <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 500 }}>
            Basic Information
          </Typography>

          <TextField
            fullWidth
            label="Field Name"
            value={formData.name}
            onChange={(e) => handleFieldChange("name", e.target.value)}
            placeholder="e.g., Priority Level, Status, Due Date"
            sx={{ mb: 2 }}
            disabled={loading}
          />

          <FormControl fullWidth sx={{ mb: 2 }}>
            <InputLabel>Field Type</InputLabel>
            <Select
              value={formData.field_type}
              label="Field Type"
              onChange={(e) => handleFieldChange("field_type", e.target.value)}
              disabled={loading}
            >
              {FIELD_TYPES.map((type) => (
                <MenuItem key={type.type} value={type.type}>
                  {type.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControlLabel
            control={
              <Switch checked={formData.is_required} onChange={(e) => handleFieldChange("is_required", e.target.checked)} disabled={loading} />
            }
            label="Required Field"
          />
        </Box>

        {/* Field Type Preview */}
        <Box mb={3}>
          <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 500 }}>
            Preview
          </Typography>
          <FieldTypePreview fieldType={formData.field_type} />
        </Box>

        {/* Choice Options for SELECT fields */}
        {selectedFieldType?.hasChoices && (
          <>
            <Divider sx={{ my: 3 }} />
            <Box>
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 500 }}>
                Choice Options
              </Typography>
              <ChoiceOptionsManager
                options={formData.choice_options || []}
                onChange={handleChoiceOptionsChange}
                disabled={loading}
                fieldType={formData.field_type}
              />
            </Box>
          </>
        )}
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={handleClose} disabled={loading} color="inherit">
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          disabled={loading || !formData.name.trim()}
          startIcon={loading ? <CircularProgress size={16} /> : null}
        >
          {loading ? "Creating..." : "Create Field"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CreateCustomFieldModal;
