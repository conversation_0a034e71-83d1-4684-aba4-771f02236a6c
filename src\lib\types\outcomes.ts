/**
 * Outcomes Types
 * Based on API documentation for /api/workitems/outcomes/
 */

export interface Outcome {
  id: string; // UUID from backend
  name: string;
  description?: string;
  project: string; // UUID reference to Project
  project_name?: string; // Project name from backend
  project_full_path?: string; // Full project path from backend
  project_details?: {
    id: string;
    name: string;
    full_path?: string;
  };
  life_aspect_name?: string; // Life aspect name from backend
  priority_level?: string; // UUID reference to PriorityLevel
  priority_level_name?: string; // Priority level name from backend
  priority_level_details?: {
    id: string;
    name: string;
    description?: string;
    display_order: number;
  };
  status: OutcomeStatus;
  due_date?: string; // ISO date string
  is_overdue?: boolean; // Computed field from backend
  sort_order?: number;
  created_at: string;
  updated_at: string;
  user?: number; // User ID (read-only)
  // Custom fields and tags support
  custom_fields?: Record<string, any>;
  tags?: string[];
  resolved_custom_fields?: any[]; // Resolved custom fields from backend
}

export type OutcomeStatus = 'NOT_STARTED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';

export interface CreateOutcomeData {
  name: string;
  description?: string;
  project: string; // UUID
  priority_level?: string; // UUID
  status?: OutcomeStatus;
  due_date?: string; // ISO date string
  sort_order?: number;
  custom_fields?: Record<string, any>;
  tags?: string[];
}

export interface UpdateOutcomeData {
  name?: string;
  description?: string;
  project?: string;
  priority_level?: string;
  status?: OutcomeStatus;
  due_date?: string;
  sort_order?: number;
  custom_fields?: Record<string, any>;
  tags?: string[];
}

export interface OutcomeError {
  message: string;
  field?: string;
  details?: Record<string, string[]>;
}

// Paginated response type for outcomes
export interface PaginatedOutcomesResponse {
  count: number;
  next: string | null;
  previous: string | null;
  results: Outcome[];
}

// Outcome completion metrics for projects
export interface ProjectCompletionMetrics {
  projectId: string;
  totalOutcomes: number;
  completedOutcomes: number;
  completionPercentage: number;
  // Includes outcomes from all sub-projects
  totalOutcomesWithChildren: number;
  completedOutcomesWithChildren: number;
  completionPercentageWithChildren: number;
}

// Helper function to calculate completion percentage
export function calculateCompletionPercentage(completed: number, total: number): number {
  if (total === 0) return 0;
  return Math.round((completed / total) * 100);
}

// Helper function to get outcome status display info
export function getOutcomeStatusInfo(status: OutcomeStatus): { label: string; color: string; icon: string } {
  switch (status) {
    case 'NOT_STARTED':
      return { label: 'Not Started', color: 'default', icon: 'radio_button_unchecked' };
    case 'IN_PROGRESS':
      return { label: 'In Progress', color: 'warning', icon: 'pending' };
    case 'COMPLETED':
      return { label: 'Completed', color: 'success', icon: 'check_circle' };
    case 'CANCELLED':
      return { label: 'Cancelled', color: 'error', icon: 'cancel' };
    default:
      return { label: 'Unknown', color: 'default', icon: 'help' };
  }
}

// Helper function to check if outcome is completed
export function isOutcomeCompleted(outcome: Outcome): boolean {
  return outcome.status === 'COMPLETED';
}
