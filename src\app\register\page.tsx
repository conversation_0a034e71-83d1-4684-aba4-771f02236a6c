"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/lib/auth/AuthContext";
import { RegisterData, AuthError } from "@/lib/types/auth";
import { Con<PERSON>er, Card, CardContent, TextField, Button, Typography, Box, Alert, CircularProgress, Stack, Divider, Grid } from "@mui/material";
import { PersonAdd, Login as LoginIcon } from "@mui/icons-material";

export default function RegisterPage() {
  const router = useRouter();
  const { register, isLoading, error, clearError } = useAuth();

  const [formData, setFormData] = useState<RegisterData>({
    username: "",
    email: "",
    password: "",
    password_confirm: "",
    first_name: "",
    last_name: "",
  });

  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string>("");

  const handleInputChange = (field: keyof RegisterData) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData((prev) => ({
      ...prev,
      [field]: event.target.value,
    }));

    // Clear field-specific error when user starts typing
    if (formErrors[field]) {
      setFormErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }

    // Clear global error
    if (error) {
      clearError();
    }

    // Clear success message
    if (successMessage) {
      setSuccessMessage("");
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.username.trim()) {
      errors.username = "Username is required";
    } else if (formData.username.length < 3) {
      errors.username = "Username must be at least 3 characters";
    }

    if (!formData.email.trim()) {
      errors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = "Please enter a valid email address";
    }

    if (!formData.password) {
      errors.password = "Password is required";
    } else if (formData.password.length < 8) {
      errors.password = "Password must be at least 8 characters";
    }

    if (!formData.password_confirm) {
      errors.password_confirm = "Please confirm your password";
    } else if (formData.password !== formData.password_confirm) {
      errors.password_confirm = "Passwords do not match";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);
      await register(formData);

      // Show success message and redirect to login
      setSuccessMessage("Registration successful! Please sign in with your new account.");

      // Redirect to login page after a short delay
      setTimeout(() => {
        router.push("/login");
      }, 2000);
    } catch (error) {
      const authError = error as AuthError;

      // Handle field-specific errors from backend
      if (authError.details) {
        const fieldErrors: Record<string, string> = {};
        Object.entries(authError.details).forEach(([field, messages]) => {
          if (messages && messages.length > 0) {
            fieldErrors[field] = messages[0];
          }
        });
        setFormErrors(fieldErrors);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Container maxWidth="md" sx={{ py: 8 }}>
      <Card
        elevation={0}
        sx={{
          borderRadius: 3,
          border: 1,
          borderColor: "divider",
        }}
      >
        <CardContent sx={{ p: 4 }}>
          <Box textAlign="center" mb={4}>
            <PersonAdd
              sx={{
                fontSize: 48,
                color: "primary.main",
                mb: 2,
              }}
            />
            <Typography variant="h4" component="h1" gutterBottom>
              Create Account
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Join us to start achieving your life goals
            </Typography>
          </Box>

          {successMessage && (
            <Alert severity="success" sx={{ mb: 3 }}>
              {successMessage}
            </Alert>
          )}

          {error && (
            <Alert severity="error" sx={{ mb: 3 }} onClose={clearError}>
              {error}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit}>
            <Stack spacing={3}>
              <Grid container spacing={2}>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <TextField
                    fullWidth
                    label="First Name"
                    value={formData.first_name}
                    onChange={handleInputChange("first_name")}
                    error={!!formErrors.first_name}
                    helperText={formErrors.first_name}
                    disabled={isLoading || isSubmitting}
                    autoComplete="given-name"
                  />
                </Grid>
                <Grid size={{ xs: 12, sm: 6 }}>
                  <TextField
                    fullWidth
                    label="Last Name"
                    value={formData.last_name}
                    onChange={handleInputChange("last_name")}
                    error={!!formErrors.last_name}
                    helperText={formErrors.last_name}
                    disabled={isLoading || isSubmitting}
                    autoComplete="family-name"
                  />
                </Grid>
              </Grid>

              <TextField
                fullWidth
                label="Username"
                value={formData.username}
                onChange={handleInputChange("username")}
                error={!!formErrors.username}
                helperText={formErrors.username}
                disabled={isLoading || isSubmitting}
                autoComplete="username"
                autoFocus
              />

              <TextField
                fullWidth
                label="Email Address"
                type="email"
                value={formData.email}
                onChange={handleInputChange("email")}
                error={!!formErrors.email}
                helperText={formErrors.email}
                disabled={isLoading || isSubmitting}
                autoComplete="email"
              />

              <TextField
                fullWidth
                label="Password"
                type="password"
                value={formData.password}
                onChange={handleInputChange("password")}
                error={!!formErrors.password}
                helperText={formErrors.password}
                disabled={isLoading || isSubmitting}
                autoComplete="new-password"
              />

              <TextField
                fullWidth
                label="Confirm Password"
                type="password"
                value={formData.password_confirm}
                onChange={handleInputChange("password_confirm")}
                error={!!formErrors.password_confirm}
                helperText={formErrors.password_confirm}
                disabled={isLoading || isSubmitting}
                autoComplete="new-password"
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                disabled={isLoading || isSubmitting || !!successMessage}
                startIcon={isLoading || isSubmitting ? <CircularProgress size={20} /> : <PersonAdd />}
                sx={{ py: 1.5 }}
              >
                {isLoading || isSubmitting ? "Creating Account..." : "Create Account"}
              </Button>
            </Stack>
          </Box>

          <Divider sx={{ my: 3 }}>
            <Typography variant="body2" color="text.secondary">
              or
            </Typography>
          </Divider>

          <Box textAlign="center">
            <Typography variant="body2" color="text.secondary" mb={2}>
              Already have an account?
            </Typography>
            <Button component={Link} href="/login" variant="outlined" startIcon={<LoginIcon />} fullWidth>
              Sign In
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
}
