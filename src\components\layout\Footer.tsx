import { Box, Typography, Container } from "@mui/material";

const Footer = () => {
  return (
    <Box
      component="footer"
      sx={{
        bgcolor: "background.paper",
        borderTop: 1,
        borderColor: "divider",
        py: 3,
        mt: "auto",
      }}
    >
      <Container maxWidth="lg">
        <Box textAlign="center">
          <Typography variant="body2" color="text.secondary">
            &copy; {new Date().getFullYear()} Agile Life Results System. Built with Next.js and Material UI.
          </Typography>
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: "block" }}>
            Achieve a balanced and fulfilling life.
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
