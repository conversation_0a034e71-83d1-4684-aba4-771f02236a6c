"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/lib/auth/AuthContext";
import { LoginCredentials, AuthError } from "@/lib/types/auth";
import { Container, Card, CardContent, TextField, Button, Typography, Box, Alert, CircularProgress, Stack, Divider } from "@mui/material";
import { Login as LoginIcon, PersonAdd } from "@mui/icons-material";

export default function LoginPage() {
  const router = useRouter();
  const { login, isLoading, error, clearError } = useAuth();

  const [formData, setFormData] = useState<LoginCredentials>({
    username: "",
    password: "",
  });

  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (field: keyof LoginCredentials) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData((prev) => ({
      ...prev,
      [field]: event.target.value,
    }));

    // Clear field-specific error when user starts typing
    if (formErrors[field]) {
      setFormErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }

    // Clear global error
    if (error) {
      clearError();
    }
  };

  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.username.trim()) {
      errors.username = "Username or email is required";
    }

    if (!formData.password) {
      errors.password = "Password is required";
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      setIsSubmitting(true);
      await login(formData);

      // Redirect to home page on successful login
      router.push("/");
    } catch (error) {
      const authError = error as AuthError;

      // Handle field-specific errors from backend
      if (authError.details) {
        const fieldErrors: Record<string, string> = {};
        Object.entries(authError.details).forEach(([field, messages]) => {
          if (messages && messages.length > 0) {
            fieldErrors[field] = messages[0];
          }
        });
        setFormErrors(fieldErrors);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Container maxWidth="sm" sx={{ py: 8 }}>
      <Card
        elevation={0}
        sx={{
          borderRadius: 3,
          border: 1,
          borderColor: "divider",
        }}
      >
        <CardContent sx={{ p: 4 }}>
          <Box textAlign="center" mb={4}>
            <LoginIcon
              sx={{
                fontSize: 48,
                color: "primary.main",
                mb: 2,
              }}
            />
            <Typography variant="h4" component="h1" gutterBottom>
              Welcome Back
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Sign in to your account to continue
            </Typography>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }} onClose={clearError}>
              {error}
            </Alert>
          )}

          <Box component="form" onSubmit={handleSubmit}>
            <Stack spacing={3}>
              <TextField
                fullWidth
                label="Username or Email"
                type="text"
                value={formData.username}
                onChange={handleInputChange("username")}
                error={!!formErrors.username}
                helperText={formErrors.username}
                disabled={isLoading || isSubmitting}
                autoComplete="username"
                autoFocus
              />

              <TextField
                fullWidth
                label="Password"
                type="password"
                value={formData.password}
                onChange={handleInputChange("password")}
                error={!!formErrors.password}
                helperText={formErrors.password}
                disabled={isLoading || isSubmitting}
                autoComplete="current-password"
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                size="large"
                disabled={isLoading || isSubmitting}
                startIcon={isLoading || isSubmitting ? <CircularProgress size={20} /> : <LoginIcon />}
                sx={{ py: 1.5 }}
              >
                {isLoading || isSubmitting ? "Signing In..." : "Sign In"}
              </Button>
            </Stack>
          </Box>

          <Divider sx={{ my: 3 }}>
            <Typography variant="body2" color="text.secondary">
              or
            </Typography>
          </Divider>

          <Box textAlign="center">
            <Typography variant="body2" color="text.secondary" mb={2}>
              Don&apos;t have an account?
            </Typography>
            <Button component={Link} href="/register" variant="outlined" startIcon={<PersonAdd />} fullWidth>
              Create Account
            </Button>
          </Box>

          <Box textAlign="center" mt={3}>
            <Button component={Link} href="#" variant="text" size="small" color="primary">
              Forgot Password?
            </Button>
          </Box>
        </CardContent>
      </Card>
    </Container>
  );
}
