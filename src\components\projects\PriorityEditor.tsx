"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  MenuList,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Chip,
  Icon,
  CircularProgress,
  Alert,
} from "@mui/material";
import { PriorityLevel, getPriorityDisplayConfig } from "@/lib/types/priorities";
import { prioritiesService } from "@/lib/api/prioritiesService";
import { projectsService } from "@/lib/api/projectsService";

interface PriorityEditorProps {
  projectId: string;
  currentPriorityId?: string;
  currentPriorityName?: string;
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
  onPriorityChanged: (newPriorityId: string, newPriorityName: string) => void;
}

const PriorityEditor: React.FC<PriorityEditorProps> = ({
  projectId,
  currentPriorityId,
  currentPriorityName,
  anchorEl,
  open,
  onClose,
  onPriorityChanged,
}) => {
  const [priorityLevels, setPriorityLevels] = useState<PriorityLevel[]>([]);
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (open) {
      fetchPriorityLevels();
    }
  }, [open]);

  const fetchPriorityLevels = async () => {
    setLoading(true);
    setError(null);
    try {
      const levels = await prioritiesService.getPriorityLevels();
      setPriorityLevels(levels);
    } catch (err: any) {
      console.error("Error fetching priority levels:", err);
      setError(err.message || "Failed to load priority levels");
    } finally {
      setLoading(false);
    }
  };

  const handlePrioritySelect = async (priorityLevel: PriorityLevel) => {
    if (priorityLevel.id === currentPriorityId) {
      onClose();
      return;
    }

    setUpdating(true);
    setError(null);
    try {
      await projectsService.updateProject(projectId, {
        priority_level: priorityLevel.id,
      });
      onPriorityChanged(priorityLevel.id, priorityLevel.name);
      onClose();
    } catch (err: any) {
      console.error("Error updating project priority:", err);
      setError(err.message || "Failed to update priority");
    } finally {
      setUpdating(false);
    }
  };

  const renderPriorityChip = (priorityName: string, isSelected: boolean = false) => {
    const config = getPriorityDisplayConfig(priorityName);
    return (
      <Chip
        icon={<Icon>{config.icon}</Icon>}
        label={priorityName}
        variant={config.chipVariant}
        color={config.color as any}
        size="small"
        sx={{
          fontWeight: isSelected ? 600 : 400,
          opacity: isSelected ? 1 : 0.8,
        }}
      />
    );
  };

  return (
    <Popover
      open={open}
      anchorEl={anchorEl}
      onClose={onClose}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "left",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "left",
      }}
      PaperProps={{
        sx: {
          minWidth: 250,
          maxWidth: 350,
          boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.15)",
          borderRadius: 2,
        },
      }}
    >
      {error && (
        <Alert severity="error" sx={{ m: 1, mb: 0 }}>
          {error}
        </Alert>
      )}
      
      {loading ? (
        <MenuItem disabled sx={{ justifyContent: "center", py: 2 }}>
          <CircularProgress size={20} />
        </MenuItem>
      ) : (
        <MenuList dense>
          {priorityLevels.map((level) => {
            const isSelected = level.id === currentPriorityId;
            const config = getPriorityDisplayConfig(level.name);
            
            return (
              <MenuItem
                key={level.id}
                onClick={() => handlePrioritySelect(level)}
                disabled={updating}
                sx={{
                  backgroundColor: isSelected ? "action.selected" : "transparent",
                  "&:hover": {
                    backgroundColor: isSelected ? "action.selected" : "action.hover",
                  },
                }}
              >
                <ListItemIcon sx={{ minWidth: 36 }}>
                  <Icon color={config.color as any}>{config.icon}</Icon>
                </ListItemIcon>
                <ListItemText
                  primary={level.name}
                  secondary={level.description}
                  primaryTypographyProps={{
                    fontWeight: isSelected ? 600 : 400,
                  }}
                />
                {updating && level.id === currentPriorityId && (
                  <CircularProgress size={16} sx={{ ml: 1 }} />
                )}
              </MenuItem>
            );
          })}
        </MenuList>
      )}
    </Popover>
  );
};

export default PriorityEditor;
