"use client";

import React, { useState } from "react";
import {
  Popover,
  TextField,
  Button,
  Box,
  Typography,
  CircularProgress,
  Alert,
} from "@mui/material";
import { projectsService } from "@/lib/api/projectsService";

interface WeekEditorProps {
  projectId: string;
  currentWeek?: number;
  anchorEl: HTMLElement | null;
  open: boolean;
  onClose: () => void;
  onWeekChanged: (newWeek: number | undefined) => void;
}

const WeekEditor: React.FC<WeekEditorProps> = ({
  projectId,
  currentWeek,
  anchorEl,
  open,
  onClose,
  onWeekChanged,
}) => {
  const [weekValue, setWeekValue] = useState<string>(currentWeek ? currentWeek.toString() : "");
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleWeekChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    // Allow empty string or numbers 1-53
    if (value === "" || (/^\d+$/.test(value) && parseInt(value) >= 1 && parseInt(value) <= 53)) {
      setWeekValue(value);
      setError(null);
    }
  };

  const handleSave = async () => {
    setUpdating(true);
    setError(null);
    try {
      const weekNumber = weekValue === "" ? undefined : parseInt(weekValue);
      await projectsService.updateProject(projectId, {
        planned_week: weekNumber,
      });
      onWeekChanged(weekNumber);
      onClose();
    } catch (err: any) {
      console.error("Error updating project week:", err);
      setError(err.message || "Failed to update planned week");
    } finally {
      setUpdating(false);
    }
  };

  const handleCancel = () => {
    setWeekValue(currentWeek ? currentWeek.toString() : "");
    setError(null);
    onClose();
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      handleSave();
    } else if (event.key === "Escape") {
      handleCancel();
    }
  };

  const getCurrentWeekNumber = () => {
    const now = new Date();
    const startOfYear = new Date(now.getFullYear(), 0, 1);
    const pastDaysOfYear = (now.getTime() - startOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + startOfYear.getDay() + 1) / 7);
  };

  const formatWeekDisplay = (week: number | undefined) => {
    if (!week) return "No week set";
    return `W${week}`;
  };

  return (
    <Popover
      open={open}
      anchorEl={anchorEl}
      onClose={handleCancel}
      anchorOrigin={{
        vertical: "bottom",
        horizontal: "left",
      }}
      transformOrigin={{
        vertical: "top",
        horizontal: "left",
      }}
      PaperProps={{
        sx: {
          p: 2,
          minWidth: 280,
          boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.15)",
          borderRadius: 2,
        },
      }}
    >
      <Typography variant="subtitle2" gutterBottom>
        Edit Planned Week
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <TextField
        fullWidth
        size="small"
        label="Week Number"
        value={weekValue}
        onChange={handleWeekChange}
        onKeyDown={handleKeyPress}
        placeholder="Enter week number (1-53)"
        helperText={`Current week: W${getCurrentWeekNumber()}`}
        disabled={updating}
        autoFocus
        sx={{ mb: 2 }}
        inputProps={{
          min: 1,
          max: 53,
        }}
      />

      <Box sx={{ display: "flex", gap: 1, justifyContent: "flex-end" }}>
        <Button
          size="small"
          onClick={handleCancel}
          disabled={updating}
        >
          Cancel
        </Button>
        <Button
          size="small"
          variant="contained"
          onClick={handleSave}
          disabled={updating}
          startIcon={updating ? <CircularProgress size={16} /> : null}
        >
          {updating ? "Saving..." : "Save"}
        </Button>
      </Box>

      <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: "block" }}>
        Current: {formatWeekDisplay(currentWeek)}
      </Typography>
    </Popover>
  );
};

export default WeekEditor;
