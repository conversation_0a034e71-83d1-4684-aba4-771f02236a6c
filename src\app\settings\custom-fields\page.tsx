"use client";

import React, { useState, useEffect } from "react";
import { Container, Typo<PERSON>, Box, Button, Alert, CircularProgress, Fab, Tooltip } from "@mui/material";
import { Add as AddIcon } from "@mui/icons-material";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import { useAuth } from "@/lib/auth/AuthContext";
import { customFieldsService } from "@/lib/api/customFieldsService";
import { CustomFieldDefinition } from "@/lib/types/customFields";
import CustomFieldDefinitionCard from "@/components/customFields/CustomFieldDefinitionCard";
import CreateCustomFieldModal from "@/components/customFields/CreateCustomFieldModal";
import EditCustomFieldModal from "@/components/customFields/EditCustomFieldModal";
import DeleteCustomFieldDialog from "@/components/customFields/DeleteCustomFieldDialog";

const CustomFieldsManagementPage = () => {
  // Get authentication state
  const { isAuthenticated, isLoading: authLoading, token } = useAuth();

  // State management
  const [definitions, setDefinitions] = useState<CustomFieldDefinition[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedDefinition, setSelectedDefinition] = useState<CustomFieldDefinition | null>(null);

  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Load custom field definitions
  const loadDefinitions = async () => {
    try {
      setLoading(true);
      setError(null);

      const allDefinitions = await customFieldsService.getCustomFieldDefinitions();
      setDefinitions(allDefinitions);
    } catch (err: any) {
      console.error("Error loading custom field definitions:", err);
      setError(err.message || "Failed to load custom field definitions");
    } finally {
      setLoading(false);
    }
  };

  // Load definitions only after authentication is ready
  useEffect(() => {
    // Only load data when authentication is complete and user is authenticated
    if (!authLoading && isAuthenticated && token) {
      loadDefinitions();
    } else if (!authLoading && !isAuthenticated) {
      // If not authenticated, set loading to false to let ProtectedRoute handle redirect
      setLoading(false);
    }
  }, [authLoading, isAuthenticated, token]);

  // Handle create custom field
  const handleCreateCustomField = () => {
    setIsCreateModalOpen(true);
  };

  // Handle edit custom field
  const handleEditCustomField = (definition: CustomFieldDefinition) => {
    setSelectedDefinition(definition);
    setIsEditModalOpen(true);
  };

  // Handle delete custom field
  const handleDeleteCustomField = (definition: CustomFieldDefinition) => {
    setSelectedDefinition(definition);
    setIsDeleteDialogOpen(true);
  };

  // Handle successful create
  const handleCreateSuccess = () => {
    setIsCreateModalOpen(false);
    loadDefinitions(); // Reload data
  };

  // Handle successful edit
  const handleEditSuccess = () => {
    setIsEditModalOpen(false);
    setSelectedDefinition(null);
    loadDefinitions(); // Reload data
  };

  // Handle successful delete
  const handleDeleteSuccess = () => {
    setIsDeleteDialogOpen(false);
    setSelectedDefinition(null);
    loadDefinitions(); // Reload data
  };

  // Handle modal close
  const handleCloseModals = () => {
    setIsCreateModalOpen(false);
    setIsEditModalOpen(false);
    setIsDeleteDialogOpen(false);
    setSelectedDefinition(null);
  };

  // Handle drag and drop reordering
  const handleReorderDefinitions = async (reorderedDefinitions: CustomFieldDefinition[]) => {
    try {
      // Update sort order based on new positions
      const updates = reorderedDefinitions.map((def, index) => ({
        id: def.id,
        sort_order: index,
      }));

      await customFieldsService.updateSortOrder(updates);

      // Update local state
      setDefinitions(reorderedDefinitions);
    } catch (err: any) {
      console.error("Error reordering definitions:", err);
      setError(err.message || "Failed to reorder custom field definitions");
    }
  };

  // Handle toggle required status
  const handleToggleRequired = async (definition: CustomFieldDefinition) => {
    try {
      const updatedDefinition = await customFieldsService.patchCustomFieldDefinition(definition.id, { is_required: !definition.is_required });

      // Update local state
      setDefinitions((prev) => prev.map((def) => (def.id === definition.id ? updatedDefinition : def)));
    } catch (err: any) {
      console.error("Error toggling required status:", err);
      setError(err.message || "Failed to update custom field definition");
    }
  };

  if (loading) {
    return (
      <ProtectedRoute>
        <Container maxWidth="lg" sx={{ py: 4 }}>
          <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
            <CircularProgress />
          </Box>
        </Container>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        {/* Page Header */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={4}>
          <Typography variant="h4" component="h1" sx={{ fontWeight: 500 }}>
            Custom Fields Management
          </Typography>

          <Button variant="contained" startIcon={<AddIcon />} onClick={handleCreateCustomField} sx={{ borderRadius: 2 }}>
            Create Custom Field
          </Button>
        </Box>

        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {/* Custom Field Definitions */}
        {definitions.length === 0 ? (
          <Alert severity="info" sx={{ mb: 2 }}>
            No custom fields defined yet. Click "Create Custom Field" to add your first field.
          </Alert>
        ) : (
          <Box display="grid" gridTemplateColumns="repeat(auto-fill, minmax(320px, 1fr))" gap={3} mb={3}>
            {definitions.map((definition) => (
              <CustomFieldDefinitionCard
                key={definition.id}
                definition={definition}
                onEdit={handleEditCustomField}
                onDelete={handleDeleteCustomField}
                onToggleRequired={handleToggleRequired}
              />
            ))}
          </Box>
        )}

        {/* Floating Action Button for Mobile */}
        <Tooltip title="Create Custom Field">
          <Fab
            color="primary"
            aria-label="create custom field"
            onClick={handleCreateCustomField}
            sx={{
              position: "fixed",
              bottom: 24,
              right: 24,
              display: { xs: "flex", md: "none" },
            }}
          >
            <AddIcon />
          </Fab>
        </Tooltip>

        {/* Modals and Dialogs */}
        <CreateCustomFieldModal open={isCreateModalOpen} onClose={handleCloseModals} onSuccess={handleCreateSuccess} />

        <EditCustomFieldModal open={isEditModalOpen} definition={selectedDefinition} onClose={handleCloseModals} onSuccess={handleEditSuccess} />

        <DeleteCustomFieldDialog
          open={isDeleteDialogOpen}
          definition={selectedDefinition}
          onClose={handleCloseModals}
          onSuccess={handleDeleteSuccess}
        />
      </Container>
    </ProtectedRoute>
  );
};

export default CustomFieldsManagementPage;
