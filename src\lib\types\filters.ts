// Filter types for the filtering system

export interface FilterRule {
  id: string; // Unique identifier for the rule
  fieldId: string; // Custom field definition ID
  fieldName: string; // Display name of the field
  fieldType: string; // Type of the field (SINGLE_SELECT, DATE, WEEK, etc.)
  condition: string; // Condition operator (is, is_not, is_before, etc.)
  value: any; // The value to filter by
}

// Condition options for different field types
export interface ConditionOption {
  value: string;
  label: string;
}

// Condition mappings for different field types
export const FIELD_CONDITIONS: Record<string, ConditionOption[]> = {
  SINGLE_SELECT: [
    { value: "is", label: "is" },
    { value: "is_not", label: "is not" },
  ],
  DATE: [
    { value: "is", label: "is" },
    { value: "is_before", label: "is before" },
    { value: "is_after", label: "is after" },
    { value: "is_on_or_before", label: "is on or before" },
    { value: "is_on_or_after", label: "is on or after" },
  ],
  WEEK: [
    { value: "is", label: "is" },
    { value: "is_before", label: "is before" },
    { value: "is_after", label: "is after" },
    { value: "is_on_or_before", label: "is on or before" },
    { value: "is_on_or_after", label: "is on or after" },
  ],
  TEXT: [
    { value: "contains", label: "contains" },
    { value: "does_not_contain", label: "does not contain" },
    { value: "is", label: "is" },
    { value: "is_not", label: "is not" },
  ],
  NUMBER: [
    { value: "is", label: "is" },
    { value: "is_not", label: "is not" },
    { value: "is_greater_than", label: "is greater than" },
    { value: "is_less_than", label: "is less than" },
    { value: "is_greater_than_or_equal", label: "is greater than or equal to" },
    { value: "is_less_than_or_equal", label: "is less than or equal to" },
  ],
};

// Filter request payload for API
export interface FilterRequest {
  filters: FilterRule[];
}

// Filter response from API
export interface FilterResponse<T> {
  results: T[];
  total_count?: number;
  has_more?: boolean;
}
