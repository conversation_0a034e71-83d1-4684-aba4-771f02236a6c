{"name": "minimisia_frontend_only", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:pull": "prisma db pull", "db:studio": "prisma studio"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hello-pangea/dnd": "^18.0.1", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@mui/x-date-pickers": "^8.5.0", "@prisma/client": "^6.1.0", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "date-fns": "^4.1.0", "jsonwebtoken": "^9.0.2", "next": "15.3.2", "node-fetch": "^3.3.2", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "prisma": "^6.1.0", "tailwindcss": "^4", "typescript": "^5"}}