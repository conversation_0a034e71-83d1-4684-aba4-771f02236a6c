import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/database/prisma";
import { verifyToken, extractTokenFromHeader } from "@/lib/auth/jwt";

// Helper function to authenticate request
async function authenticateRequest(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  const token = extractTokenFromHeader(authHeader);

  if (!token) {
    return { error: NextResponse.json({ message: "Authentication required" }, { status: 401 }) };
  }

  const payload = verifyToken(token);
  if (!payload) {
    return { error: NextResponse.json({ message: "Invalid or expired token" }, { status: 401 }) };
  }

  return { userId: payload.userId };
}

// Helper function to find and verify life aspect ownership
async function findAndVerifyLifeAspect(id: string, userId: string) {
  const lifeAspect = await prisma.workitems_life_aspect.findUnique({
    where: { id },
  });

  if (!lifeAspect) {
    return { error: NextResponse.json({ message: "Life aspect not found" }, { status: 404 }) };
  }

  if (lifeAspect.user_id !== userId) {
    return { error: NextResponse.json({ message: "Access denied" }, { status: 403 }) };
  }

  return { lifeAspect };
}

interface LifeAspectUpdateInput {
  name?: string;
  color?: string;
  sort_order?: number;
}

// GET /api/workitems/life-aspects/[id] - Get specific life aspect
export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    console.log(`GET /api/workitems/life-aspects/${id} called`);

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Find and verify life aspect
    const result = await findAndVerifyLifeAspect(id, auth.userId);
    if (result.error) return result.error;

    return NextResponse.json(result.lifeAspect);
  } catch (error) {
    console.error("Error fetching life aspect:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// PATCH /api/workitems/life-aspects/[id] - Update life aspect
export async function PATCH(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    console.log(`PATCH /api/workitems/life-aspects/${id} called`);

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Find and verify life aspect
    const result = await findAndVerifyLifeAspect(id, auth.userId);
    if (result.error) return result.error;

    // Parse request body
    const body = (await request.json()) as LifeAspectUpdateInput;
    console.log("Request body:", body);

    // Validate input
    if (body.name !== undefined) {
      if (typeof body.name !== "string" || body.name.trim().length === 0) {
        return NextResponse.json(
          {
            message: "Life aspect name must be a non-empty string",
          },
          { status: 400 }
        );
      }

      if (body.name.length > 100) {
        return NextResponse.json(
          {
            message: "Life aspect name must be less than 100 characters",
          },
          { status: 400 }
        );
      }

      // Check for duplicate name (excluding current life aspect)
      const existingLifeAspect = await prisma.workitems_life_aspect.findFirst({
        where: {
          user_id: auth.userId,
          name: body.name,
          id: { not: id },
        },
      });

      if (existingLifeAspect) {
        return NextResponse.json(
          {
            message: "A life aspect with this name already exists",
          },
          { status: 400 }
        );
      }
    }

    // Prepare update data
    const updateData: any = {};
    if (body.name !== undefined) updateData.name = body.name.trim();
    if (body.color !== undefined) updateData.color = body.color;
    if (body.sort_order !== undefined) updateData.sort_order = body.sort_order;

    // Update life aspect
    const updatedLifeAspect = await prisma.workitems_life_aspect.update({
      where: { id },
      data: updateData,
    });

    console.log("Updated life aspect:", updatedLifeAspect.id);

    return NextResponse.json(updatedLifeAspect);
  } catch (error) {
    console.error("Error updating life aspect:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// DELETE /api/workitems/life-aspects/[id] - Delete life aspect
export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    console.log(`DELETE /api/workitems/life-aspects/${id} called`);

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Find and verify life aspect
    const result = await findAndVerifyLifeAspect(id, auth.userId);
    if (result.error) return result.error;

    // Get all projects associated with this life aspect
    const associatedProjects = await prisma.workitems_project.findMany({
      where: { life_aspect_id: id },
      select: { id: true },
    });

    // Delete all outcomes associated with these projects first (cascade)
    if (associatedProjects.length > 0) {
      const projectIds = associatedProjects.map((p) => p.id);
      await prisma.workitems_outcome.deleteMany({
        where: { project_id: { in: projectIds } },
      });

      // Delete all projects associated with this life aspect
      await prisma.workitems_project.deleteMany({
        where: { life_aspect_id: id },
      });
    }

    // Delete life aspect
    await prisma.workitems_life_aspect.delete({
      where: { id },
    });

    console.log("Deleted life aspect:", id);

    return NextResponse.json({ message: "Life aspect deleted successfully" });
  } catch (error) {
    console.error("Error deleting life aspect:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
