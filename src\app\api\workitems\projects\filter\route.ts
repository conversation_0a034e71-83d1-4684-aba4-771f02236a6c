import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/database/prisma";
import { verifyToken, extractTokenFromHeader } from "@/lib/auth/jwt";
import { resolveCustomFields } from "@/lib/utils/customFieldResolver";
import { FilterRule } from "@/lib/types/filters";

// Helper function to authenticate request
async function authenticateRequest(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  const token = extractTokenFromHeader(authHeader);

  if (!token) {
    return { error: NextResponse.json({ message: "Authentication required" }, { status: 401 }) };
  }

  const payload = verifyToken(token);
  if (!payload) {
    return { error: NextResponse.json({ message: "Invalid or expired token" }, { status: 401 }) };
  }

  return { userId: payload.userId };
}

// Helper function to build project hierarchy tree
function buildProjectTree(projects: any[]): any[] {
  const projectMap = new Map();
  const rootProjects: any[] = [];

  // First pass: create map of all projects
  projects.forEach((project) => {
    projectMap.set(project.id, {
      ...project,
      sub_projects: [],
    });
  });

  // Second pass: build hierarchy
  projects.forEach((project) => {
    const projectNode = projectMap.get(project.id);

    if (project.parent_project_id) {
      const parent = projectMap.get(project.parent_project_id);
      if (parent) {
        parent.sub_projects.push(projectNode);
      } else {
        // Parent not found, treat as root
        rootProjects.push(projectNode);
      }
    } else {
      rootProjects.push(projectNode);
    }
  });

  return rootProjects;
}

// Helper function to build Prisma where clause from filter rules
function buildFilterWhereClause(filters: FilterRule[], userId: string) {
  const baseWhere = { user_id: userId };

  if (!filters || filters.length === 0) {
    return baseWhere;
  }

  const filterConditions = filters.map((filter) => {
    const { fieldId, condition, value } = filter;

    // Build JSON path for custom field values
    const jsonPath = `$.${fieldId}`;

    switch (condition) {
      case "is":
        return {
          custom_field_values: {
            path: [fieldId],
            equals: value,
          },
        };

      case "is_not":
        return {
          NOT: {
            custom_field_values: {
              path: [fieldId],
              equals: value,
            },
          },
        };

      case "is_before":
        return {
          custom_field_values: {
            path: [fieldId],
            lt: value,
          },
        };

      case "is_after":
        return {
          custom_field_values: {
            path: [fieldId],
            gt: value,
          },
        };

      case "is_on_or_before":
        return {
          custom_field_values: {
            path: [fieldId],
            lte: value,
          },
        };

      case "is_on_or_after":
        return {
          custom_field_values: {
            path: [fieldId],
            gte: value,
          },
        };

      case "contains":
        return {
          custom_field_values: {
            path: [fieldId],
            string_contains: value,
          },
        };

      case "does_not_contain":
        return {
          NOT: {
            custom_field_values: {
              path: [fieldId],
              string_contains: value,
            },
          },
        };

      case "is_greater_than":
        return {
          custom_field_values: {
            path: [fieldId],
            gt: value,
          },
        };

      case "is_less_than":
        return {
          custom_field_values: {
            path: [fieldId],
            lt: value,
          },
        };

      case "is_greater_than_or_equal":
        return {
          custom_field_values: {
            path: [fieldId],
            gte: value,
          },
        };

      case "is_less_than_or_equal":
        return {
          custom_field_values: {
            path: [fieldId],
            lte: value,
          },
        };

      default:
        console.warn(`Unknown filter condition: ${condition}`);
        return {};
    }
  });

  // Combine all filter conditions with AND logic
  return {
    ...baseWhere,
    AND: filterConditions,
  };
}

// POST /api/workitems/projects/filter/ - Get filtered projects in hierarchical structure
export async function POST(request: NextRequest) {
  try {
    console.log("POST /api/workitems/projects/filter/ called");

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Parse request body
    const body = await request.json();
    const { filters } = body;

    console.log("Received filters:", filters);

    // Validate filters
    if (!Array.isArray(filters)) {
      return NextResponse.json({ message: "Filters must be an array" }, { status: 400 });
    }

    // Build where clause from filters
    const whereClause = buildFilterWhereClause(filters, auth.userId);
    console.log("Built where clause:", JSON.stringify(whereClause, null, 2));

    // Fetch all life aspects for the user
    const lifeAspects = await prisma.workitems_life_aspect.findMany({
      where: { user_id: auth.userId },
      orderBy: { sort_order: "asc" },
    });

    // Fetch filtered projects
    const projects = await prisma.workitems_project.findMany({
      where: whereClause,
      include: {
        outcomes: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: [{ sort_order: "asc" }, { created_at: "asc" }],
    });

    console.log(`Found ${projects.length} projects matching filters`);

    // Resolve custom fields for all projects
    const projectsWithCustomFields = await Promise.all(
      projects.map(async (project) => {
        const resolvedCustomFields = await resolveCustomFields(project.custom_field_values, "PROJECT", auth.userId);
        return {
          ...project,
          resolved_custom_fields: resolvedCustomFields,
        };
      })
    );

    // Group projects by life aspect - format for frontend compatibility
    const lifeAspectGroups = lifeAspects.map((lifeAspect) => {
      const lifeAspectProjects = projectsWithCustomFields.filter((project) => project.life_aspect_id === lifeAspect.id);

      const hierarchicalProjects = buildProjectTree(lifeAspectProjects);

      return {
        life_aspect: {
          id: lifeAspect.id,
          name: lifeAspect.name,
          color: lifeAspect.color,
          description: lifeAspect.description || "",
          sort_order: lifeAspect.sort_order,
          created_at: lifeAspect.created_at,
          updated_at: lifeAspect.updated_at,
          user: lifeAspect.user_id,
        },
        projects: hierarchicalProjects,
      };
    });

    // Filter out life aspects with no projects (when filters are applied)
    const filteredGroups = lifeAspectGroups.filter((group) => group.projects.length > 0);

    console.log(`Built filtered hierarchy with ${filteredGroups.length} life aspects`);

    // Return array format expected by frontend
    return NextResponse.json(filteredGroups);
  } catch (error) {
    console.error("Error filtering projects:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
