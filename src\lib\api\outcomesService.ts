import apiClient from "./apiClient";
import { 
  Outcome, 
  CreateOutcomeData, 
  UpdateOutcomeData, 
  OutcomeError,
  PaginatedOutcomesResponse,
  ProjectCompletionMetrics,
  isOutcomeCompleted
} from "@/lib/types/outcomes";
import { API_ENDPOINTS } from "@/lib/utils/constants";
import { AxiosError } from "axios";

/**
 * Parse API errors into a consistent format
 */
const parseApiError = (error: AxiosError): OutcomeError => {
  console.error("OutcomesService: API Error:", error);

  if (error.response?.data) {
    const data = error.response.data as any;

    // Handle validation errors with field details
    if (data.errors || data.detail || data.non_field_errors) {
      return {
        message: data.detail || "Validation error occurred",
        details: data.errors || data,
      };
    }

    // Handle simple error messages
    if (data.message || data.detail || data.error) {
      return { message: data.message || data.detail || data.error };
    }
  }

  // Default error message
  return {
    message: error.response?.status === 401 ? "Unauthorized access" : "An unexpected error occurred. Please try again.",
  };
};

export const outcomesService = {
  /**
   * Get all outcomes for the authenticated user
   */
  async getOutcomes(params?: { project?: string; status?: string }): Promise<Outcome[]> {
    try {
      const response = await apiClient.get<PaginatedOutcomesResponse | Outcome[]>(API_ENDPOINTS.WORK_ITEMS.OUTCOMES, { params });

      console.log("Outcomes API response:", response.data);

      // Handle both paginated and direct array responses
      let outcomes: Outcome[];

      if (Array.isArray(response.data)) {
        // Direct array response
        outcomes = response.data;
      } else if (response.data && "results" in response.data) {
        // Paginated response
        outcomes = response.data.results;
      } else {
        console.warn("Unexpected outcomes response format:", response.data);
        outcomes = [];
      }

      console.log("Processed outcomes:", outcomes);
      return outcomes;
    } catch (error) {
      console.error("Error fetching outcomes:", error);
      throw parseApiError(error as AxiosError);
    }
  },

  /**
   * Get outcomes for a specific project (including sub-projects)
   */
  async getOutcomesForProject(projectId: string): Promise<Outcome[]> {
    try {
      return await this.getOutcomes({ project: projectId });
    } catch (error) {
      throw parseApiError(error as AxiosError);
    }
  },

  /**
   * Get a specific outcome by ID
   */
  async getOutcome(id: string): Promise<Outcome> {
    try {
      const response = await apiClient.get<Outcome>(`${API_ENDPOINTS.WORK_ITEMS.OUTCOMES}${id}/`);
      return response.data;
    } catch (error) {
      throw parseApiError(error as AxiosError);
    }
  },

  /**
   * Create a new outcome
   */
  async createOutcome(data: CreateOutcomeData): Promise<Outcome> {
    try {
      const response = await apiClient.post<Outcome>(API_ENDPOINTS.WORK_ITEMS.OUTCOMES, data);
      return response.data;
    } catch (error) {
      throw parseApiError(error as AxiosError);
    }
  },

  /**
   * Update an existing outcome
   */
  async updateOutcome(id: string, data: UpdateOutcomeData): Promise<Outcome> {
    try {
      const response = await apiClient.patch<Outcome>(`${API_ENDPOINTS.WORK_ITEMS.OUTCOMES}${id}/`, data);
      return response.data;
    } catch (error) {
      throw parseApiError(error as AxiosError);
    }
  },

  /**
   * Delete an outcome
   */
  async deleteOutcome(id: string): Promise<void> {
    try {
      await apiClient.delete(`${API_ENDPOINTS.WORK_ITEMS.OUTCOMES}${id}/`);
    } catch (error) {
      throw parseApiError(error as AxiosError);
    }
  },

  /**
   * Calculate completion metrics for a project and its sub-projects
   */
  async calculateProjectCompletionMetrics(projectId: string, allOutcomes: Outcome[], projectIds: string[]): Promise<ProjectCompletionMetrics> {
    // Filter outcomes for this project and its children
    const projectOutcomes = allOutcomes.filter(outcome => outcome.project === projectId);
    const childProjectOutcomes = allOutcomes.filter(outcome => projectIds.includes(outcome.project));
    
    // Calculate metrics for this project only
    const completedOutcomes = projectOutcomes.filter(isOutcomeCompleted).length;
    const totalOutcomes = projectOutcomes.length;
    
    // Calculate metrics including children
    const completedOutcomesWithChildren = childProjectOutcomes.filter(isOutcomeCompleted).length;
    const totalOutcomesWithChildren = childProjectOutcomes.length;
    
    return {
      projectId,
      totalOutcomes,
      completedOutcomes,
      completionPercentage: totalOutcomes > 0 ? Math.round((completedOutcomes / totalOutcomes) * 100) : 0,
      totalOutcomesWithChildren,
      completedOutcomesWithChildren,
      completionPercentageWithChildren: totalOutcomesWithChildren > 0 ? Math.round((completedOutcomesWithChildren / totalOutcomesWithChildren) * 100) : 0,
    };
  },
};
