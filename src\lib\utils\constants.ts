// API endpoints - Updated to match backend API documentation
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: "/users/login/", // Token-based login endpoint
    REGISTER: "/users/register/", // User registration endpoint
    LOGOUT: "/users/logout/", // Logout endpoint (if available)
    USER_PROFILE: "/users/me/", // Current user profile endpoint
  },
  USERS: {
    PREFERENCES: "/users/me/preferences/", // User preferences endpoint
  },
  WORK_ITEMS: {
    LIFE_ASPECTS: "/workitems/life-aspects/", // Life Aspects management
    PRIORITY_LEVELS: "/workitems/priority-levels/", // Priority Levels management (legacy)
    PROJECTS: "/workitems/projects/", // Projects management
    PROJECTS_HIERARCHY: "/workitems/projects/hierarchy/", // Project hierarchy view
    OUTCOMES: "/workitems/outcomes/", // Outcomes management
    CUSTOM_FIELDS: "/workitems/custom-fields/", // Custom Fields management
  },
  // Add more endpoints as needed
} as const;

// Local storage keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: "authToken",
  AUTH_USER: "authUser",
} as const;

// App configuration
export const APP_CONFIG = {
  NAME: "Agile Life Results System",
  DESCRIPTION: "Achieve a balanced and fulfilling life.",
} as const;
