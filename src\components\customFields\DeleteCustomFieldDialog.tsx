"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, DialogTitle, DialogContent, <PERSON>alogActions, Button, Typography, Box, Alert, CircularProgress, Chip } from "@mui/material";
import { Warning as WarningIcon, Delete as DeleteIcon } from "@mui/icons-material";
import { customFieldsService } from "@/lib/api/customFieldsService";
import { CustomFieldDefinition, FIELD_TYPES } from "@/lib/types/customFields";

interface DeleteCustomFieldDialogProps {
  open: boolean;
  definition: CustomFieldDefinition | null;
  onClose: () => void;
  onSuccess: () => void;
}

const DeleteCustomFieldDialog: React.FC<DeleteCustomFieldDialogProps> = ({ open, definition, onClose, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Get field type info
  const fieldTypeInfo = definition ? FIELD_TYPES.find((type) => type.type === definition.field_type) : null;

  // Handle delete confirmation
  const handleDelete = async () => {
    if (!definition) return;

    try {
      setLoading(true);
      setError(null);

      await customFieldsService.deleteCustomFieldDefinition(definition.id);
      onSuccess();
    } catch (err: any) {
      console.error("Error deleting custom field:", err);
      setError(err.message || "Failed to delete custom field");
    } finally {
      setLoading(false);
    }
  };

  // Handle dialog close
  const handleClose = () => {
    if (!loading) {
      setError(null);
      onClose();
    }
  };

  if (!definition) {
    return null;
  }

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 },
      }}
    >
      <DialogTitle sx={{ pb: 2 }}>
        <Box display="flex" alignItems="center" gap={1}>
          <WarningIcon color="error" />
          <Typography variant="h6" component="h2">
            Delete Custom Field
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ pt: 0 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="subtitle2" sx={{ mb: 1 }}>
            This action cannot be undone!
          </Typography>
          <Typography variant="body2">
            Deleting this custom field will permanently remove all associated data from your projects, outcomes, or weekly plans.
          </Typography>
        </Alert>

        {/* Field Details */}
        <Box sx={{ p: 2, backgroundColor: "grey.50", borderRadius: 1, mb: 2 }}>
          <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 500 }}>
            Field to be deleted:
          </Typography>

          <Box mb={2}>
            <Typography variant="h6" component="h3" sx={{ fontWeight: 500 }}>
              {definition.name}
            </Typography>
          </Box>

          <Box display="flex" gap={1} mb={2}>
            <Chip label={fieldTypeInfo?.label || definition.field_type} size="small" variant="outlined" sx={{ borderRadius: 1 }} />
            {definition.is_required && <Chip label="Required" size="small" color="error" variant="outlined" sx={{ borderRadius: 1 }} />}
          </Box>

          {/* Show choice options if applicable */}
          {definition.choice_options && definition.choice_options.length > 0 && (
            <Box>
              <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: "block" }}>
                Choice Options ({definition.choice_options.length}):
              </Typography>
              <Box display="flex" flexWrap="wrap" gap={0.5}>
                {definition.choice_options
                  .sort((a, b) => a.sort_order - b.sort_order)
                  .slice(0, 3)
                  .map((option) => (
                    <Chip
                      key={option.id}
                      label={option.value}
                      size="small"
                      sx={{
                        backgroundColor: option.color,
                        color: "white",
                        fontSize: "0.7rem",
                        height: 18,
                      }}
                    />
                  ))}
                {definition.choice_options.length > 3 && (
                  <Chip
                    label={`+${definition.choice_options.length - 3} more`}
                    size="small"
                    variant="outlined"
                    sx={{
                      fontSize: "0.7rem",
                      height: 18,
                    }}
                  />
                )}
              </Box>
            </Box>
          )}
        </Box>

        <Typography variant="body2" color="text.secondary">
          Are you sure you want to delete this custom field? This will remove the field definition and all its associated data.
        </Typography>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={handleClose} disabled={loading} color="inherit">
          Cancel
        </Button>
        <Button
          onClick={handleDelete}
          variant="contained"
          color="error"
          disabled={loading}
          startIcon={loading ? <CircularProgress size={16} /> : <DeleteIcon />}
        >
          {loading ? "Deleting..." : "Delete Field"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DeleteCustomFieldDialog;
