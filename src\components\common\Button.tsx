import { ReactNode } from "react";
import { <PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>, CircularProgress } from "@mui/material";
import { ButtonProps as MuiButtonProps } from "@mui/material/Button";

interface ButtonProps extends Omit<MuiButtonProps, "variant" | "size"> {
  variant?: "primary" | "secondary" | "outline" | "danger";
  size?: "sm" | "md" | "lg";
  isLoading?: boolean;
  children: ReactNode;
}

const Button = ({ variant = "primary", size = "md", isLoading = false, children, disabled, ...props }: ButtonProps) => {
  // Map custom variants to MUI variants
  const muiVariant = (() => {
    switch (variant) {
      case "primary":
        return "contained";
      case "secondary":
        return "contained";
      case "outline":
        return "outlined";
      case "danger":
        return "contained";
      default:
        return "contained";
    }
  })();

  // Map custom sizes to MUI sizes
  const muiSize = (() => {
    switch (size) {
      case "sm":
        return "small";
      case "md":
        return "medium";
      case "lg":
        return "large";
      default:
        return "medium";
    }
  })();

  // Map custom variants to MUI colors
  const muiColor = (() => {
    switch (variant) {
      case "primary":
        return "primary";
      case "secondary":
        return "secondary";
      case "outline":
        return "primary";
      case "danger":
        return "error";
      default:
        return "primary";
    }
  })();

  return (
    <MuiButton
      variant={muiVariant}
      size={muiSize}
      color={muiColor}
      disabled={disabled || isLoading}
      startIcon={isLoading ? <CircularProgress size={16} /> : undefined}
      sx={{
        textTransform: "none",
        borderRadius: 2,
      }}
      {...props}
    >
      {children}
    </MuiButton>
  );
};

export default Button;
