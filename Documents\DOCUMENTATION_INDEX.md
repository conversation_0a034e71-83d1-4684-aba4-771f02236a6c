# Documentation Index

## 📚 Complete Documentation Overview

This document provides an index of all available documentation for the Minimisia Frontend project, a modern Next.js full-stack application with Material-UI and Prisma ORM.

## 🏗️ Architecture & Setup

### [README.md](../README.md)
**Main project documentation**
- Project overview and features
- Tech stack (Next.js 15.3.2, Material-UI v7, Prisma ORM)
- Installation and setup instructions
- Development workflow

### [MIGRATION_GUIDE.md](MIGRATION_GUIDE.md)
**Django to Next.js migration documentation**
- Architecture comparison (before/after)
- Breaking changes and compatibility notes
- Migration checklist and implementation details
- Benefits of the new architecture

## 🎨 Design & UI/UX

### [STYLE_GUIDE.md](STYLE_GUIDE.md)
**Comprehensive design system documentation**
- Material Design 3 principles and minimalistic aesthetic
- Color palette and typography scale
- Component styling guidelines (buttons, cards, navigation)
- Accessibility standards (WCAG AA compliance)
- Material-UI v7 theme configuration

## 🔧 API Documentation

### [USING_CUSTOM_APIS.md](USING_CUSTOM_APIS.md)
**Complete developer guide for Custom Fields API**
- 11 supported field types with examples
- CRUD operations for custom field definitions
- Choice options management with drag & drop
- Frontend integration patterns and validation
- Performance optimization strategies
- Recent bug fixes and updates

### [USER_PREFERENCES_API.md](USER_PREFERENCES_API.md)
**User preferences system documentation**
- API endpoints for user customization settings
- Custom field visibility and pinning preferences
- JWT authentication requirements
- Database schema with Prisma models
- Frontend integration examples

## 🚀 Implementation Summaries

### [CUSTOM_FIELDS_IMPLEMENTATION_SUMMARY.md](CUSTOM_FIELDS_IMPLEMENTATION_SUMMARY.md)
**High-level overview of Custom Fields system**
- Architecture summary with Prisma ORM
- Core models and database schema
- API implementation with Next.js routes
- Frontend integration with Material-UI
- Performance considerations and security features

### [CUSTOM_FIELDS_MANAGEMENT_IMPLEMENTATION.md](CUSTOM_FIELDS_MANAGEMENT_IMPLEMENTATION.md)
**Custom Fields Management UI implementation**
- Card-based UI design with Material-UI v7
- Component architecture and file structure
- CRUD operations with optimistic updates
- Responsive design and accessibility features
- Testing recommendations and demo instructions

## 📋 Quick Reference

### Current Tech Stack
- **Framework**: Next.js 15.3.2 (App Router)
- **Language**: TypeScript
- **UI Library**: Material-UI (MUI) v7
- **Styling**: Material-UI theme + Tailwind CSS v4
- **Database**: PostgreSQL with Prisma ORM v6
- **Authentication**: JWT tokens with bcrypt
- **State Management**: React hooks and context
- **HTTP Client**: Axios
- **Drag & Drop**: @hello-pangea/dnd v18

### Key Features Implemented
- ✅ JWT-based authentication system
- ✅ Hierarchical project management
- ✅ Dynamic custom fields system (11 field types)
- ✅ User preferences and customization
- ✅ Responsive Material-UI design
- ✅ Custom fields management interface
- ✅ Project overview with sticky tables
- ✅ Life Aspects categorization

### API Endpoints Overview
```
Authentication:
├── POST /api/users/login
├── POST /api/users/register
└── GET  /api/users/me

User Preferences:
├── GET   /api/users/me/preferences
├── PUT   /api/users/me/preferences
└── PATCH /api/users/me/preferences

Custom Fields:
├── GET    /api/workitems/custom-fields
├── POST   /api/workitems/custom-fields
├── PUT    /api/workitems/custom-fields/{id}
└── DELETE /api/workitems/custom-fields/{id}

Projects:
├── GET    /api/workitems/projects
├── POST   /api/workitems/projects
├── PATCH  /api/workitems/projects/{id}
└── DELETE /api/workitems/projects/{id}

Life Aspects:
├── GET    /api/workitems/life-aspects
├── POST   /api/workitems/life-aspects
└── PUT    /api/workitems/life-aspects/{id}
```

## 🎯 Documentation Status

### ✅ Up-to-Date Documents
All documentation has been updated to reflect the current Next.js full-stack architecture:

1. **README.md** - ✅ Updated tech stack and setup
2. **STYLE_GUIDE.md** - ✅ Material-UI v7 and design system
3. **USING_CUSTOM_APIS.md** - ✅ Next.js API routes and JWT auth
4. **USER_PREFERENCES_API.md** - ✅ Prisma schema and endpoints
5. **CUSTOM_FIELDS_IMPLEMENTATION_SUMMARY.md** - ✅ Current architecture
6. **CUSTOM_FIELDS_MANAGEMENT_IMPLEMENTATION.md** - ✅ UI implementation
7. **MIGRATION_GUIDE.md** - ✅ New migration documentation
8. **DOCUMENTATION_INDEX.md** - ✅ This overview document

### 🔄 Recent Updates (June 2025)
- Migrated from Django REST Framework to Next.js API routes
- Updated authentication from Token to JWT Bearer
- Upgraded Material-UI from v5 to v7
- Added Tailwind CSS v4 integration
- Updated all API endpoint references
- Added Prisma ORM documentation
- Enhanced security and performance documentation

## 📖 How to Use This Documentation

### For Developers
1. Start with **README.md** for project setup
2. Review **MIGRATION_GUIDE.md** to understand architecture changes
3. Use **USING_CUSTOM_APIS.md** for API integration
4. Follow **STYLE_GUIDE.md** for UI development

### For Designers
1. Review **STYLE_GUIDE.md** for design system
2. Check **CUSTOM_FIELDS_MANAGEMENT_IMPLEMENTATION.md** for UI patterns
3. Use Material-UI v7 documentation for component details

### For Project Managers
1. Read **README.md** for feature overview
2. Check implementation summaries for progress tracking
3. Review **MIGRATION_GUIDE.md** for technical decisions

## 🔗 External Resources

### Official Documentation
- [Next.js 15 Documentation](https://nextjs.org/docs)
- [Material-UI v7 Documentation](https://mui.com/)
- [Prisma ORM Documentation](https://www.prisma.io/docs)
- [TypeScript Documentation](https://www.typescriptlang.org/docs)

### Design Resources
- [Material Design 3](https://m3.material.io/)
- [Material Icons](https://fonts.google.com/icons)
- [Inter Font Family](https://fonts.google.com/specimen/Inter)

---

**Documentation Last Updated**: June 14, 2025
**Project Status**: Production Ready
**Architecture**: Next.js Full-Stack with Prisma ORM
