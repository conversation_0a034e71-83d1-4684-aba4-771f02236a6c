"use client";

import React, { useState, useEffect } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  IconButton,
  Divider,
} from "@mui/material";
import {
  Add as AddIcon,
  Close as CloseIcon,
} from "@mui/icons-material";
import { FilterRule } from "@/lib/types/filters";
import FilterRuleRow from "./FilterRuleRow";
import { customFieldsService } from "@/lib/api/customFieldsService";
import { CustomFieldDefinition } from "@/lib/types/customFields";

interface ProjectFilterModalProps {
  open: boolean;
  onClose: () => void;
  onFiltersApplied: (filters: FilterRule[]) => void;
  initialFilters?: FilterRule[];
}

const ProjectFilterModal: React.FC<ProjectFilterModalProps> = ({
  open,
  onClose,
  onFiltersApplied,
  initialFilters = [],
}) => {
  const [filters, setFilters] = useState<FilterRule[]>(initialFilters);
  const [customFields, setCustomFields] = useState<CustomFieldDefinition[]>([]);
  const [loading, setLoading] = useState(false);

  // Load custom fields when modal opens
  useEffect(() => {
    if (open) {
      loadCustomFields();
      setFilters(initialFilters);
    }
  }, [open, initialFilters]);

  const loadCustomFields = async () => {
    try {
      setLoading(true);
      const fields = await customFieldsService.getCustomFieldDefinitions();
      setCustomFields(fields);
    } catch (error) {
      console.error("Error loading custom fields:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddFilter = () => {
    const newFilter: FilterRule = {
      id: `filter_${Date.now()}`,
      fieldId: "",
      fieldName: "",
      fieldType: "",
      condition: "",
      value: null,
    };
    setFilters([...filters, newFilter]);
  };

  const handleUpdateFilter = (filterId: string, updatedFilter: Partial<FilterRule>) => {
    setFilters(filters.map(filter => 
      filter.id === filterId 
        ? { ...filter, ...updatedFilter }
        : filter
    ));
  };

  const handleRemoveFilter = (filterId: string) => {
    setFilters(filters.filter(filter => filter.id !== filterId));
  };

  const handleApplyFilters = () => {
    // Only include complete filters (with all required fields)
    const completeFilters = filters.filter(filter => 
      filter.fieldId && filter.condition && filter.value !== null && filter.value !== ""
    );
    onFiltersApplied(completeFilters);
  };

  const handleClearFilters = () => {
    setFilters([]);
    onFiltersApplied([]);
  };

  const hasCompleteFilters = filters.some(filter => 
    filter.fieldId && filter.condition && filter.value !== null && filter.value !== ""
  );

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          minHeight: "400px",
        },
      }}
    >
      <DialogTitle sx={{ 
        display: "flex", 
        justifyContent: "space-between", 
        alignItems: "center",
        pb: 1,
      }}>
        <Typography variant="h6" component="div">
          Filter Projects
        </Typography>
        <IconButton
          onClick={onClose}
          size="small"
          sx={{ color: "text.secondary" }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <Divider />

      <DialogContent sx={{ py: 3 }}>
        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
          {/* Filter Rules */}
          {filters.map((filter, index) => (
            <FilterRuleRow
              key={filter.id}
              filter={filter}
              customFields={customFields}
              onUpdate={(updatedFilter) => handleUpdateFilter(filter.id, updatedFilter)}
              onRemove={() => handleRemoveFilter(filter.id)}
              isFirst={index === 0}
            />
          ))}

          {/* Add Filter Button */}
          <Button
            variant="outlined"
            startIcon={<AddIcon />}
            onClick={handleAddFilter}
            sx={{
              alignSelf: "flex-start",
              textTransform: "none",
              borderRadius: 2,
              borderStyle: "dashed",
              color: "text.secondary",
              borderColor: "divider",
              "&:hover": {
                borderColor: "primary.main",
                backgroundColor: "primary.light",
                color: "primary.main",
              },
            }}
          >
            Add Filter
          </Button>

          {/* Empty State */}
          {filters.length === 0 && (
            <Box sx={{ 
              textAlign: "center", 
              py: 4,
              color: "text.secondary",
            }}>
              <Typography variant="body1" sx={{ mb: 1 }}>
                No filters applied
              </Typography>
              <Typography variant="body2">
                Click "Add Filter" to start filtering your projects
              </Typography>
            </Box>
          )}
        </Box>
      </DialogContent>

      <Divider />

      <DialogActions sx={{ p: 3, gap: 1 }}>
        <Button
          onClick={handleClearFilters}
          disabled={filters.length === 0}
          sx={{ textTransform: "none" }}
        >
          Clear All
        </Button>
        <Box sx={{ flex: 1 }} />
        <Button
          onClick={onClose}
          sx={{ textTransform: "none" }}
        >
          Cancel
        </Button>
        <Button
          onClick={handleApplyFilters}
          variant="contained"
          disabled={!hasCompleteFilters}
          sx={{ 
            textTransform: "none",
            minWidth: 100,
          }}
        >
          Apply Filters
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProjectFilterModal;
