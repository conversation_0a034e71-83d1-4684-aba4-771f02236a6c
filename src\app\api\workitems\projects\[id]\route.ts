import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/database/prisma";
import { verifyToken, extractTokenFromHeader } from "@/lib/auth/jwt";
import { validateUpdateProject, ValidationError } from "@/lib/validation/projects";
import { resolveCustomFields, processCustomFieldInputs } from "@/lib/utils/customFieldResolver";
import { UpdateProjectData } from "@/lib/types/projects";

// Helper function to authenticate request
async function authenticateRequest(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  const token = extractTokenFromHeader(authHeader);

  if (!token) {
    return { error: NextResponse.json({ message: "Authentication required" }, { status: 401 }) };
  }

  const payload = verifyToken(token);
  if (!payload) {
    return { error: NextResponse.json({ message: "Invalid or expired token" }, { status: 401 }) };
  }

  return { userId: payload.userId };
}

// Helper function to create validation error response
function createValidationErrorResponse(errors: ValidationError[]) {
  const errorMap: Record<string, string[]> = {};
  errors.forEach((error) => {
    if (!errorMap[error.field]) errorMap[error.field] = [];
    errorMap[error.field].push(error.message);
  });

  return NextResponse.json(
    {
      message: "Validation failed",
      errors: errorMap,
    },
    { status: 400 }
  );
}

// Helper function to find and verify ownership of project
async function findAndVerifyProject(id: string, userId: string) {
  const project = await prisma.workitems_project.findUnique({
    where: { id },
    include: {
      life_aspect: true,
      parent_project: {
        select: {
          id: true,
          name: true,
        },
      },
      sub_projects: {
        select: {
          id: true,
          name: true,
          sort_order: true,
        },
        orderBy: { sort_order: "asc" },
      },
      outcomes: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  });

  if (!project) {
    return { error: NextResponse.json({ message: "Project not found" }, { status: 404 }) };
  }

  if (project.user_id !== userId) {
    return { error: NextResponse.json({ message: "Access forbidden" }, { status: 403 }) };
  }

  return { project };
}

// GET /api/workitems/projects/[id] - Get single project
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    console.log(`GET /api/workitems/projects/${params.id} called`);

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Find and verify project
    const result = await findAndVerifyProject(params.id, auth.userId);
    if (result.error) return result.error;

    // Resolve custom fields
    const resolvedCustomFields = await resolveCustomFields(result.project.custom_field_values as Record<string, any>, "PROJECT", auth.userId);

    const projectWithCustomFields = {
      ...result.project,
      // Ensure dates are in YYYY-MM-DD format for frontend consistency
      start_date: result.project.start_date ? result.project.start_date.toISOString().split("T")[0] : null,
      end_date: result.project.end_date ? result.project.end_date.toISOString().split("T")[0] : null,
      resolved_custom_fields: resolvedCustomFields,
    };

    // Debug: Log the project data being returned including dates
    console.log("🔍 GET DEBUG: Project data being returned:", {
      id: result.project.id,
      name: result.project.name,
      start_date: result.project.start_date,
      end_date: result.project.end_date,
      hasStartDate: !!result.project.start_date,
      hasEndDate: !!result.project.end_date,
    });

    console.log("Found project:", result.project.id);

    return NextResponse.json(projectWithCustomFields);
  } catch (error) {
    console.error("Error fetching project:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// PATCH /api/workitems/projects/[id] - Update project
export async function PATCH(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    console.log(`PATCH /api/workitems/projects/${params.id} called`);

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Find and verify project
    const result = await findAndVerifyProject(params.id, auth.userId);
    if (result.error) return result.error;

    // Parse request body
    const body = (await request.json()) as UpdateProjectData;
    console.log("Request body:", { ...body, custom_field_inputs: body.custom_field_inputs?.length || 0 });

    // Validate input
    const validationErrors = validateUpdateProject(body);
    if (validationErrors.length > 0) {
      return createValidationErrorResponse(validationErrors);
    }

    // Prepare update data
    const updateData: any = {};

    if (body.name !== undefined) updateData.name = body.name;
    if (body.description !== undefined) updateData.description = body.description;
    if (body.start_date !== undefined) updateData.start_date = body.start_date ? new Date(body.start_date) : null;
    if (body.end_date !== undefined) updateData.end_date = body.end_date ? new Date(body.end_date) : null;
    if (body.sort_order !== undefined) updateData.sort_order = body.sort_order;

    // Debug: Log prepared update data
    console.log("🔍 Backend DEBUG: Prepared updateData:", JSON.stringify(updateData, null, 2));

    // Verify life aspect exists and belongs to user (if being updated)
    if (body.life_aspect !== undefined) {
      const lifeAspect = await prisma.workitems_life_aspect.findFirst({
        where: {
          id: body.life_aspect,
          user_id: auth.userId,
        },
      });

      if (!lifeAspect) {
        return NextResponse.json(
          {
            message: "Life aspect not found or does not belong to user",
          },
          { status: 400 }
        );
      }

      updateData.life_aspect_id = body.life_aspect;
    }

    // Verify parent project exists and belongs to user (if being updated)
    if (body.parent !== undefined) {
      if (body.parent === null) {
        updateData.parent_project_id = null;
      } else {
        // Check for circular reference
        if (body.parent === params.id) {
          return NextResponse.json(
            {
              message: "A project cannot be its own parent",
            },
            { status: 400 }
          );
        }

        const parentProject = await prisma.workitems_project.findFirst({
          where: {
            id: body.parent,
            user_id: auth.userId,
          },
        });

        if (!parentProject) {
          return NextResponse.json(
            {
              message: "Parent project not found or does not belong to user",
            },
            { status: 400 }
          );
        }

        updateData.parent_project_id = body.parent;
      }
    }

    // Process custom field inputs
    if (body.custom_field_inputs !== undefined) {
      try {
        console.log("🔍 DEBUG: Processing custom field inputs:", body.custom_field_inputs);

        // Log DATE field inputs specifically
        const dateFields = body.custom_field_inputs.filter((input) => {
          // We'll need to check field type, but for now log all inputs
          return true;
        });
        console.log("🔍 DEBUG: All custom field inputs:", dateFields);

        const existingValues = result.project.custom_field_values as Record<string, any>;
        console.log("🔍 DEBUG: Existing custom field values:", existingValues);

        const updatedValues = await processCustomFieldInputs(body.custom_field_inputs, "PROJECT", auth.userId, existingValues);
        console.log("🔍 DEBUG: Updated custom field values:", updatedValues);

        updateData.custom_field_values = updatedValues;
      } catch (error) {
        console.error("❌ Custom field processing error:", error);
        return NextResponse.json(
          {
            message: "Custom field validation failed",
            error: error instanceof Error ? error.message : "Unknown error",
          },
          { status: 400 }
        );
      }
    }

    // Update project
    const updatedProject = await prisma.workitems_project.update({
      where: { id: params.id },
      data: updateData,
      include: {
        life_aspect: true,
        parent_project: {
          select: {
            id: true,
            name: true,
          },
        },
        sub_projects: {
          select: {
            id: true,
            name: true,
            sort_order: true,
          },
          orderBy: { sort_order: "asc" },
        },
        outcomes: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Resolve custom fields
    const resolvedCustomFields = await resolveCustomFields(updatedProject.custom_field_values as Record<string, any>, "PROJECT", auth.userId);

    const projectWithCustomFields = {
      ...updatedProject,
      // Ensure dates are in YYYY-MM-DD format for frontend consistency
      start_date: updatedProject.start_date ? updatedProject.start_date.toISOString().split("T")[0] : null,
      end_date: updatedProject.end_date ? updatedProject.end_date.toISOString().split("T")[0] : null,
      resolved_custom_fields: resolvedCustomFields,
    };

    // Debug: Log the saved project data including dates
    console.log("🔍 Backend DEBUG: Saved project data:", {
      id: updatedProject.id,
      name: updatedProject.name,
      start_date: updatedProject.start_date,
      end_date: updatedProject.end_date,
      updated_at: updatedProject.updated_at,
    });

    console.log("Updated project:", updatedProject.id);

    return NextResponse.json(projectWithCustomFields);
  } catch (error) {
    console.error("Error updating project:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

// DELETE /api/workitems/projects/[id] - Delete project
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    console.log(`DELETE /api/workitems/projects/${params.id} called`);

    // Authenticate request
    const auth = await authenticateRequest(request);
    if (auth.error) return auth.error;

    // Find and verify project
    const result = await findAndVerifyProject(params.id, auth.userId);
    if (result.error) return result.error;

    // Check if project has sub-projects
    const subProjectsCount = await prisma.workitems_project.count({
      where: { parent_project_id: params.id },
    });

    if (subProjectsCount > 0) {
      return NextResponse.json(
        {
          message: "Cannot delete project with sub-projects. Please delete or move sub-projects first.",
        },
        { status: 400 }
      );
    }

    // Delete project (outcomes will be deleted automatically due to cascade)
    await prisma.workitems_project.delete({
      where: { id: params.id },
    });

    console.log("Deleted project:", params.id);

    return NextResponse.json({ message: "Project deleted successfully" });
  } catch (error) {
    console.error("Error deleting project:", error);
    return NextResponse.json(
      {
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
