"use client";

import React from "react";
import { Container, Typography, Box, Card, CardContent, CardActions, Button } from "@mui/material";
import { Settings as SettingsIcon, Extension as ExtensionIcon, Tune as TuneIcon } from "@mui/icons-material";
import { useRouter } from "next/navigation";
import ProtectedRoute from "@/components/auth/ProtectedRoute";

const SettingsPage = () => {
  const router = useRouter();

  const settingsOptions = [
    {
      title: "Preferences",
      description: "Customize your application settings including week start preferences and display options",
      icon: <TuneIcon />,
      path: "/settings/preferences",
      available: true,
    },
    {
      title: "Custom Fields Management",
      description: "Create and manage custom fields for projects, outcomes, and weekly planning",
      icon: <ExtensionIcon />,
      path: "/settings/custom-fields",
      available: true,
    },
    // Add more settings options here in the future
  ];

  return (
    <ProtectedRoute>
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 500, mb: 3 }}>
          Settings
        </Typography>

        <Box display="grid" gridTemplateColumns="repeat(auto-fill, minmax(300px, 1fr))" gap={3}>
          {settingsOptions.map((option) => (
            <Card
              key={option.path}
              sx={{
                transition: "all 0.2s ease-in-out",
                "&:hover": {
                  transform: "translateY(-2px)",
                  boxShadow: (theme) => theme.shadows[8],
                },
              }}
            >
              <CardContent>
                <Box display="flex" alignItems="center" gap={2} mb={2}>
                  {option.icon}
                  <Typography variant="h6" component="h2" sx={{ fontWeight: 500 }}>
                    {option.title}
                  </Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  {option.description}
                </Typography>
              </CardContent>
              <CardActions>
                <Button variant="contained" onClick={() => router.push(option.path)} disabled={!option.available} sx={{ ml: 1, mb: 1 }}>
                  {option.available ? "Open" : "Coming Soon"}
                </Button>
              </CardActions>
            </Card>
          ))}
        </Box>
      </Container>
    </ProtectedRoute>
  );
};

export default SettingsPage;
