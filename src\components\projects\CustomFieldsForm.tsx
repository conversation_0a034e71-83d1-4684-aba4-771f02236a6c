"use client";

import React from "react";
import {
  Box,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  Typography,
  Chip,
  OutlinedInput,
  SelectChangeEvent,
  Autocomplete,
} from "@mui/material";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { CustomFieldDefinition, ResolvedCustomField, CustomFieldInput } from "@/lib/types/customFields";
import { apiDateToDateObject, dateObjectToApiFormat } from "@/lib/utils/dateUtils";
import WeekPicker from "@/components/forms/WeekPicker";

interface CustomFieldsFormProps {
  customFieldDefinitions: CustomFieldDefinition[];
  currentValues?: ResolvedCustomField[];
  customFieldInputs?: CustomFieldInput[];
  onChange?: (customFieldInputs: CustomFieldInput[]) => void;
  onCustomFieldInputsChange?: (customFieldInputs: CustomFieldInput[]) => void;
  disabled?: boolean;
}

const CustomFieldsForm: React.FC<CustomFieldsFormProps> = ({
  customFieldDefinitions,
  currentValues,
  customFieldInputs,
  onChange,
  onCustomFieldInputsChange,
  disabled = false,
}) => {
  // Determine the change handler to use
  const handleChange = onChange || onCustomFieldInputsChange;

  // Convert current values to a map for easy lookup
  const currentValuesMap = React.useMemo(() => {
    const map: Record<string, any> = {};

    // Start with currentValues (existing project data) if available
    if (currentValues) {
      currentValues.forEach((field) => {
        // Ensure SINGLE_SELECT fields always store only the string ID
        if (field.field_type === "SINGLE_SELECT") {
          if (typeof field.value === "object" && field.value !== null && field.value.id) {
            map[field.definition_id] = field.value.id;
          } else {
            map[field.definition_id] = field.value;
          }
        } else {
          map[field.definition_id] = field.value;
        }
      });
    }

    // Override with any customFieldInputs (user changes) - this is crucial for edit mode
    if (customFieldInputs && customFieldInputs.length > 0) {
      customFieldInputs.forEach((input) => {
        map[input.definition_id] = input.value;
      });
    }

    return map;
  }, [currentValues, customFieldInputs]);

  // Handle field value changes
  const handleFieldChange = (definitionId: string, value: any) => {
    // Ensure we're working with the correct field definition
    const definition = customFieldDefinitions.find((def) => def.id === definitionId);
    if (!definition) return;

    // For SINGLE_SELECT, ensure we're only storing the string ID
    let processedValue = value;
    if (definition.field_type === "SINGLE_SELECT" && value !== null) {
      processedValue = typeof value === "object" && value.id ? value.id : String(value || "");
    }

    // Debug logging for SINGLE_SELECT fields
    if (definition.field_type === "SINGLE_SELECT") {
      console.log(`🔄 SINGLE_SELECT field "${definition.name}" change:`, {
        definitionId,
        originalValue: value,
        processedValue,
        valueType: typeof processedValue,
      });
    }

    // Create updated custom field inputs array
    const updatedInputs: CustomFieldInput[] = customFieldDefinitions.map((def) => ({
      definition_id: def.id,
      value: def.id === definitionId ? processedValue : currentValuesMap[def.id] ?? null,
    }));

    if (handleChange) {
      handleChange(updatedInputs);
    }
  };

  // Render field based on type
  const renderField = (definition: CustomFieldDefinition) => {
    const currentValue = currentValuesMap[definition.id];
    const fieldId = `custom-field-${definition.id}`;

    switch (definition.field_type) {
      case "TEXT":
      case "EMAIL":
      case "URL":
      case "PHONE":
        return (
          <TextField
            key={definition.id}
            id={fieldId}
            label={definition.name}
            type={definition.field_type === "EMAIL" ? "email" : definition.field_type === "URL" ? "url" : "text"}
            value={currentValue || ""}
            onChange={(e) => handleFieldChange(definition.id, e.target.value)}
            required={definition.is_required}
            disabled={disabled}
            fullWidth
            variant="outlined"
            size="small"
          />
        );

      case "TEXTAREA":
        return (
          <TextField
            key={definition.id}
            id={fieldId}
            label={definition.name}
            value={currentValue || ""}
            onChange={(e) => handleFieldChange(definition.id, e.target.value)}
            required={definition.is_required}
            disabled={disabled}
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            size="small"
          />
        );

      case "NUMBER":
        return (
          <TextField
            key={definition.id}
            id={fieldId}
            label={definition.name}
            type="number"
            value={currentValue || ""}
            onChange={(e) => handleFieldChange(definition.id, e.target.value ? Number(e.target.value) : null)}
            required={definition.is_required}
            disabled={disabled}
            fullWidth
            variant="outlined"
            size="small"
          />
        );

      case "BOOLEAN":
        return (
          <FormControlLabel
            key={definition.id}
            control={
              <Checkbox checked={Boolean(currentValue)} onChange={(e) => handleFieldChange(definition.id, e.target.checked)} disabled={disabled} />
            }
            label={definition.name}
          />
        );

      case "DATE":
        return (
          <LocalizationProvider key={definition.id} dateAdapter={AdapterDateFns}>
            <DatePicker
              label={definition.name}
              value={apiDateToDateObject(currentValue)}
              onChange={(date) => handleFieldChange(definition.id, dateObjectToApiFormat(date))}
              disabled={disabled}
              format="E, d MMM, yy"
              slotProps={{
                textField: {
                  fullWidth: true,
                  size: "small",
                  required: definition.is_required,
                  placeholder: "Mon, 16 Jun, 25",
                },
              }}
            />
          </LocalizationProvider>
        );

      case "DATETIME":
        return (
          <LocalizationProvider key={definition.id} dateAdapter={AdapterDateFns}>
            <DateTimePicker
              label={definition.name}
              value={currentValue ? new Date(currentValue) : null}
              onChange={(date) => handleFieldChange(definition.id, date ? date.toISOString() : null)}
              disabled={disabled}
              slotProps={{
                textField: {
                  fullWidth: true,
                  size: "small",
                  required: definition.is_required,
                },
              }}
            />
          </LocalizationProvider>
        );

      case "WEEK":
        return (
          <WeekPicker
            key={definition.id}
            label={definition.name}
            value={currentValue || null}
            onChange={(value) => handleFieldChange(definition.id, value)}
            disabled={disabled}
            required={definition.is_required}
            fullWidth
            size="small"
          />
        );

      case "SINGLE_SELECT":
        // Handle both cases: currentValue as string (ID) or object with id property
        const selectedValueId = typeof currentValue === "string" ? currentValue : currentValue?.id || "";

        return (
          <FormControl key={definition.id} fullWidth size="small" required={definition.is_required}>
            <InputLabel id={`${fieldId}-label`}>{definition.name}</InputLabel>
            <Select
              labelId={`${fieldId}-label`}
              id={fieldId}
              value={selectedValueId}
              label={definition.name}
              onChange={(e) => {
                // Always store only the choice option ID string as required by the API
                handleFieldChange(definition.id, e.target.value || null);
              }}
              disabled={disabled}
            >
              <MenuItem value="">
                <em>None</em>
              </MenuItem>
              {definition.choice_options?.map((option) => (
                <MenuItem key={option.id} value={option.id}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: "50%",
                        backgroundColor: option.color,
                        border: "1px solid #e0e0e0",
                      }}
                    />
                    {option.value}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );

      case "MULTI_SELECT":
        const selectedValues = Array.isArray(currentValue) ? currentValue.map((v: any) => v.id || v) : [];
        return (
          <FormControl key={definition.id} fullWidth size="small" required={definition.is_required}>
            <InputLabel id={`${fieldId}-label`}>{definition.name}</InputLabel>
            <Select
              labelId={`${fieldId}-label`}
              id={fieldId}
              multiple
              value={selectedValues}
              onChange={(e) => {
                const selectedIds = e.target.value as string[];
                const selectedOptions = definition.choice_options?.filter((opt) => selectedIds.includes(opt.id)) || [];
                handleFieldChange(
                  definition.id,
                  selectedOptions.map((opt) => opt.id)
                );
              }}
              input={<OutlinedInput label={definition.name} />}
              renderValue={(selected) => (
                <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                  {(selected as string[]).map((value) => {
                    const option = definition.choice_options?.find((opt) => opt.id === value);
                    return (
                      <Chip
                        key={value}
                        label={option?.value || value}
                        size="small"
                        sx={{
                          backgroundColor: option?.color || "#e0e0e0",
                          color: "#fff",
                          "& .MuiChip-deleteIcon": {
                            color: "#fff",
                          },
                        }}
                      />
                    );
                  })}
                </Box>
              )}
              disabled={disabled}
            >
              {definition.choice_options?.map((option) => (
                <MenuItem key={option.id} value={option.id}>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: "50%",
                        backgroundColor: option.color,
                        border: "1px solid #e0e0e0",
                      }}
                    />
                    {option.value}
                  </Box>
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );

      default:
        return (
          <TextField
            key={definition.id}
            id={fieldId}
            label={definition.name}
            value={currentValue || ""}
            onChange={(e) => handleFieldChange(definition.id, e.target.value)}
            required={definition.is_required}
            disabled={disabled}
            fullWidth
            variant="outlined"
            size="small"
          />
        );
    }
  };

  if (customFieldDefinitions.length === 0) {
    return (
      <Box sx={{ py: 2, textAlign: "center" }}>
        <Typography variant="body2" color="text.secondary">
          No custom fields defined for projects.
        </Typography>
      </Box>
    );
  }

  // Filter out Start Date and Due Date fields (now handled as standard project fields)
  const filteredDefinitions = customFieldDefinitions.filter((definition) => definition.name !== "Start Date" && definition.name !== "Due Date");

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
      {filteredDefinitions.sort((a, b) => (a.sort_order || 0) - (b.sort_order || 0)).map((definition) => renderField(definition))}
    </Box>
  );
};

export default CustomFieldsForm;
